/* Dashboard Sidebar - Professional Dark Theme */
.dashboard-sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  border-right: 1px solid #334155;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Header */
.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid #334155;
  background: rgba(59, 130, 246, 0.1);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-image {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  object-fit: cover;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  padding: 2px;
  border: 2px solid rgba(59, 130, 246, 0.3);
}

.logo-text {
  font-size: 18px;
  font-weight: 700;
  color: white;
  letter-spacing: -0.5px;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  margin: 0 12px;
  border: none;
  background: none;
  color: #94a3b8;
  text-align: left;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  width: calc(100% - 24px);
}

.sidebar-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #e2e8f0;
  transform: translateX(2px);
}

.sidebar-item.active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.sidebar-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.sidebar-item-label {
  font-weight: 500;
  white-space: nowrap;
}

/* Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #334155;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-user {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  font-size: 12px;
  color: #94a3b8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.logout-btn {
  color: #f87171 !important;
}

.logout-btn:hover {
  background: rgba(248, 113, 113, 0.1) !important;
  color: #fca5a5 !important;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 60px;
  }
  
  .sidebar-item-label,
  .user-info {
    display: none;
  }
  
  .sidebar-user {
    justify-content: center;
  }
  
  .sidebar-item {
    justify-content: center;
    margin: 0 8px;
    width: calc(100% - 16px);
  }
}

/* Animation */
.dashboard-sidebar {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
