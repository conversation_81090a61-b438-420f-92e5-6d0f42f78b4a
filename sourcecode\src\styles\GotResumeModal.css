.got-resume-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.got-resume-modal {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-number.active {
  background: #4A90E2;
  color: white;
}

.sign-in-btn {
  background: transparent;
  border: 1px solid #4A90E2;
  color: #4A90E2;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sign-in-btn:hover {
  background: #4A90E2;
  color: white;
}

.modal-content {
  padding: 60px 40px;
  text-align: center;
}

.logo-section {
  margin-bottom: 30px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.modal-content h2 {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 40px;
}

.button-group {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.yes-btn, .no-btn {
  padding: 16px 40px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 120px;
}

.yes-btn {
  background: #4A90E2;
  color: white;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.yes-btn:hover {
  background: #357ABD;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.no-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.no-btn:hover {
  background: #e9ecef;
  color: #495057;
}

@media (max-width: 768px) {
  .got-resume-modal {
    width: 95%;
    margin: 20px;
  }
  
  .modal-content {
    padding: 40px 30px;
  }
  
  .modal-content h2 {
    font-size: 28px;
  }
  
  .button-group {
    flex-direction: column;
    gap: 15px;
  }
  
  .yes-btn, .no-btn {
    width: 100%;
  }
}
