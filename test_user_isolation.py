#!/usr/bin/env python3
"""
CRITICAL TEST: Verify users get their own resumes back, not someone else's
This test uploads multiple resumes and verifies no cross-contamination
"""

import requests
import json
import time
import uuid
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Configuration
BASE_URL = "http://localhost:3001"

def create_unique_resume(user_name, profession):
    """Create a unique resume file for testing"""
    unique_id = str(uuid.uuid4())[:8]
    content = f"""
RESUME FOR: {user_name}
PROFESSION: {profession}
UNIQUE ID: {unique_id}
EMAIL: {user_name.lower().replace(' ', '.')}@email.com
PHONE: ******-{unique_id[:4]}

EXPERIENCE:
- {profession} at Company ABC (2020-2024)
- Specialized in {profession.lower()} tasks
- Unique identifier: {unique_id}

SKILLS:
- {profession} expertise
- Problem solving
- Communication
- Unique skill: {unique_id}

EDUCATION:
- Bachelor's in {profession}
- University of {user_name.split()[0]}
- Graduated: 2020
- Student ID: {unique_id}

UNIQUE MARKER: {user_name}-{profession}-{unique_id}
"""
    
    filename = f"test_resume_{user_name.replace(' ', '_')}_{unique_id}.txt"
    with open(filename, 'w') as f:
        f.write(content)
    
    return filename, unique_id, user_name, profession

def upload_and_track(user_data):
    """Upload a resume and track the token"""
    filename, unique_id, user_name, profession = user_data
    
    print(f"🚀 {user_name}: Starting upload...")
    
    try:
        # Upload the file
        with open(filename, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{BASE_URL}/upload", files=files)
        
        if response.status_code == 200:
            data = response.json()
            tracking_token = data.get('trackingToken')
            
            if tracking_token:
                print(f"✅ {user_name}: Upload successful, token: {tracking_token}")
                return {
                    'user_name': user_name,
                    'profession': profession,
                    'unique_id': unique_id,
                    'tracking_token': tracking_token,
                    'filename': filename,
                    'upload_success': True
                }
            else:
                print(f"❌ {user_name}: No tracking token received!")
                print(f"Response: {json.dumps(data, indent=2)}")
                return {'user_name': user_name, 'upload_success': False, 'error': 'No token'}
        else:
            print(f"❌ {user_name}: Upload failed: {response.status_code}")
            return {'user_name': user_name, 'upload_success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        print(f"❌ {user_name}: Upload error: {e}")
        return {'user_name': user_name, 'upload_success': False, 'error': str(e)}
    finally:
        # Cleanup file
        try:
            import os
            os.remove(filename)
        except:
            pass

def retrieve_and_verify(user_data, max_attempts=15):
    """Retrieve resume by token and verify it belongs to the correct user"""
    if not user_data.get('upload_success'):
        return user_data
    
    user_name = user_data['user_name']
    tracking_token = user_data['tracking_token']
    expected_unique_id = user_data['unique_id']
    expected_profession = user_data['profession']
    
    print(f"🔍 {user_name}: Polling for resume with token {tracking_token}")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{BASE_URL}/resume-by-token", params={'token': tracking_token})
            
            if response.status_code == 200:
                resume_data = response.json()
                
                # Extract resume details
                resume_name = resume_data.get('basics', {}).get('name', 'Unknown')
                resume_title = resume_data.get('basics', {}).get('title', 'Unknown')
                
                print(f"✅ {user_name}: Retrieved resume for: {resume_name} ({resume_title})")
                
                # Verify this is the correct resume
                is_correct = False
                contamination_detected = False
                
                # Check if the resume contains the expected user's information
                resume_str = json.dumps(resume_data).lower()
                expected_markers = [
                    user_name.lower(),
                    expected_profession.lower(),
                    expected_unique_id.lower()
                ]
                
                matches = sum(1 for marker in expected_markers if marker in resume_str)
                
                if matches >= 2:  # At least 2 out of 3 markers should match
                    is_correct = True
                    print(f"✅ {user_name}: Resume verification PASSED ({matches}/3 markers found)")
                else:
                    print(f"❌ {user_name}: Resume verification FAILED ({matches}/3 markers found)")
                    print(f"Expected markers: {expected_markers}")
                    print(f"Resume name: {resume_name}, title: {resume_title}")
                    contamination_detected = True
                
                user_data.update({
                    'retrieval_success': True,
                    'resume_name': resume_name,
                    'resume_title': resume_title,
                    'is_correct_resume': is_correct,
                    'contamination_detected': contamination_detected,
                    'attempts_needed': attempt + 1
                })
                
                return user_data
                
            elif response.status_code == 404:
                print(f"⏳ {user_name}: Attempt {attempt + 1}: Resume still processing...")
                time.sleep(10)  # Wait 10 seconds
            else:
                print(f"❌ {user_name}: Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ {user_name}: Retrieval error: {e}")
    
    print(f"❌ {user_name}: Failed to retrieve resume after {max_attempts} attempts")
    user_data.update({
        'retrieval_success': False,
        'error': f'Timeout after {max_attempts} attempts'
    })
    
    return user_data

def test_user_isolation():
    """Main test function"""
    print("🧪 CRITICAL TEST: User Isolation Verification")
    print("=" * 60)
    
    # Create test users with unique data
    test_users = [
        ("Alice Johnson", "Software Engineer"),
        ("Bob Smith", "Data Scientist"),
        ("Charlie Brown", "Product Manager")
    ]
    
    print(f"📝 Creating {len(test_users)} unique test resumes...")
    
    # Create unique resume files
    user_data_list = []
    for user_name, profession in test_users:
        user_data = create_unique_resume(user_name, profession)
        user_data_list.append(user_data)
    
    print(f"🚀 Uploading {len(user_data_list)} resumes concurrently...")
    
    # Upload all resumes concurrently
    upload_results = []
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(upload_and_track, user_data) for user_data in user_data_list]
        
        for future in as_completed(futures):
            result = future.result()
            upload_results.append(result)
    
    # Check upload results
    successful_uploads = [r for r in upload_results if r.get('upload_success')]
    print(f"\n📊 Upload Results: {len(successful_uploads)}/{len(upload_results)} successful")
    
    if len(successful_uploads) == 0:
        print("❌ No successful uploads - cannot test user isolation")
        return
    
    # Wait for processing to begin
    print("⏳ Waiting 30 seconds for processing to begin...")
    time.sleep(30)
    
    print(f"🔍 Retrieving and verifying {len(successful_uploads)} resumes...")
    
    # Retrieve and verify all resumes concurrently
    final_results = []
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(retrieve_and_verify, user_data) for user_data in successful_uploads]
        
        for future in as_completed(futures):
            result = future.result()
            final_results.append(result)
    
    # Analyze results
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print("=" * 60)
    
    total_tests = len(final_results)
    successful_retrievals = len([r for r in final_results if r.get('retrieval_success')])
    correct_resumes = len([r for r in final_results if r.get('is_correct_resume')])
    contamination_cases = len([r for r in final_results if r.get('contamination_detected')])
    
    print(f"Total tests: {total_tests}")
    print(f"Successful retrievals: {successful_retrievals}/{total_tests}")
    print(f"Correct resumes: {correct_resumes}/{successful_retrievals}")
    print(f"Cross-contamination detected: {contamination_cases}")
    
    print("\nDetailed Results:")
    for result in final_results:
        user_name = result['user_name']
        if result.get('retrieval_success'):
            status = "✅ PASS" if result.get('is_correct_resume') else "❌ FAIL"
            resume_name = result.get('resume_name', 'Unknown')
            attempts = result.get('attempts_needed', 'N/A')
            print(f"{status} {user_name}: Got resume for '{resume_name}' (attempts: {attempts})")
        else:
            print(f"❌ FAIL {user_name}: Could not retrieve resume")
    
    # Final verdict
    if contamination_cases == 0 and correct_resumes == successful_retrievals:
        print(f"\n🎉 SUCCESS: User isolation is working correctly!")
        print(f"✅ All {correct_resumes} users received their own resumes")
        print(f"✅ No cross-contamination detected")
    else:
        print(f"\n🚨 FAILURE: User isolation is NOT working!")
        print(f"❌ {contamination_cases} cases of cross-contamination detected")
        print(f"❌ Only {correct_resumes}/{successful_retrievals} users got correct resumes")

if __name__ == "__main__":
    test_user_isolation()
