.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 40px;
  height: 80px;
  background: transparent;
  border: none;
  box-shadow: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.logo {
  display: flex;
  align-items: center;
}

.logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-weight: 700;
  font-size: 20px;
  transition: color 0.2s;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo a:hover {
  color: rgba(255, 255, 255, 0.9);
}

.logo svg {
  margin-right: 10px;
}

.logo img, .logo-image {
  margin-right: 10px;
  max-height: 40px; /* Reduced logo size */
  object-fit: contain;
  border-radius: 4px;
  transition: transform 0.3s ease;
  background: transparent;
}

.logo a:hover img {
  transform: scale(1.05);
}

.nav ul {
  display: flex;
  list-style: none;
  gap: 40px;
}

.nav a {
  text-decoration: none;
  color: var(--color-text-secondary);
  font-weight: 500;
  font-size: 15px;
  transition: all 0.2s;
  position: relative;
  padding: 5px 0;
}

.nav a:hover {
  color: var(--color-primary);
}

.nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--color-primary);
  transition: width 0.3s;
}

.nav a:hover::after {
  width: 100%;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.new-resume-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: var(--shadow-md);
}

.new-resume-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.signin-btn {
  padding: 10px 24px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.signin-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-email {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.signout-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.signout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.search-box {
  position: relative;
  width: 220px;
}

.search-box input {
  width: 100%;
  padding: 10px 15px;
  padding-right: 40px;
  border: 1px solid #eaeaea;
  border-radius: 50px;
  font-size: 14px;
  outline: none;
  transition: all 0.3s;
  background-color: #f8f9fa;
}

.search-box input:focus {
  border-color: var(--color-primary);
  background-color: white;
  box-shadow: 0 2px 10px rgba(58, 110, 165, 0.1);
}

.search-box svg {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

/* Responsive styles */
@media (max-width: 768px) {
  .header {
    padding: 0 20px;
  }

  .nav {
    display: none;
  }

  .search-box {
    display: none;
  }
}
