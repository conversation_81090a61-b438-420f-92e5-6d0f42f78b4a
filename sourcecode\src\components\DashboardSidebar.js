import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import '../styles/DashboardSidebar.css';

const DashboardSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  const menuItems = [
    {
      id: 'dashboard',
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <rect x="3" y="3" width="7" height="7"/>
          <rect x="14" y="3" width="7" height="7"/>
          <rect x="14" y="14" width="7" height="7"/>
          <rect x="3" y="14" width="7" height="7"/>
        </svg>
      ),
      label: 'Dashboard',
      path: '/dashboard'
    },
    {
      id: 'create',
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="16"/>
          <line x1="8" y1="12" x2="16" y2="12"/>
        </svg>
      ),
      label: 'Create Resume',
      action: 'create'
    },
    {
      id: 'templates',
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
          <polyline points="10,9 9,9 8,9"/>
        </svg>
      ),
      label: 'Templates',
      path: '/templates'
    },
    {
      id: 'settings',
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="m12 1 1.68 3.36L17 6.64l-1.32 3.36L19 12l-3.32 1.68L14.36 17l-3.36-1.32L9 19l-1.68-3.32L4 14.36l1.32-3.36L3 9l3.32-1.68L7.64 4l3.36 1.32z"/>
        </svg>
      ),
      label: 'Settings',
      path: '/settings'
    }
  ];

  const handleItemClick = (item) => {
    if (item.action === 'create') {
      // Navigate to dashboard and trigger create modal
      navigate('/dashboard', { state: { openCreateModal: true } });
    } else if (item.path) {
      navigate(item.path);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <div className="dashboard-sidebar">
      {/* Logo/Brand */}
      <div className="sidebar-header">
        <div className="sidebar-logo">
          <img
            src="/logo.jpg"
            alt="Chambers_V Logo"
            className="logo-image"
          />
          <span className="logo-text">Chambers_V</span>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        {menuItems.map((item) => (
          <button
            key={item.id}
            className={`sidebar-item ${location.pathname === item.path ? 'active' : ''}`}
            onClick={() => handleItemClick(item)}
            title={item.label}
          >
            <div className="sidebar-item-icon">
              {item.icon}
            </div>
            <span className="sidebar-item-label">{item.label}</span>
          </button>
        ))}
      </nav>

      {/* User Section */}
      <div className="sidebar-footer">
        <div className="sidebar-user">
          <div className="user-avatar">
            {user?.profile?.name?.charAt(0) || user?.profile?.email?.charAt(0) || 'U'}
          </div>
          <div className="user-info">
            <div className="user-name">{user?.profile?.name || 'User'}</div>
            <div className="user-email">{user?.profile?.email || ''}</div>
          </div>
        </div>
        
        <button 
          className="sidebar-item logout-btn"
          onClick={handleLogout}
          title="Logout"
        >
          <div className="sidebar-item-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
              <polyline points="16,17 21,12 16,7"/>
              <line x1="21" y1="12" x2="9" y2="12"/>
            </svg>
          </div>
          <span className="sidebar-item-label">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default DashboardSidebar;
