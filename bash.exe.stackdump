Stack trace:
Frame         Function      Args
0007FFFFA0F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8FF0) msys-2.0.dll+0x1FE8E
0007FFFFA0F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3C8) msys-2.0.dll+0x67F9
0007FFFFA0F0  000210046832 (000210286019, 0007FFFF9FA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA0F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA0F0  000210068E24 (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3D0  00021006A225 (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF946900000 ntdll.dll
7FF945C50000 KERNEL32.DLL
7FF943B90000 KERNELBASE.dll
7FF944D80000 USER32.dll
7FF944100000 win32u.dll
7FF9451A0000 GDI32.dll
000210040000 msys-2.0.dll
7FF943A50000 gdi32full.dll
7FF944280000 msvcp_win.dll
7FF944130000 ucrtbase.dll
7FF9458E0000 advapi32.dll
7FF944930000 msvcrt.dll
7FF9446A0000 sechost.dll
7FF9459A0000 RPCRT4.dll
7FF943050000 CRYPTBASE.DLL
7FF9443C0000 bcryptPrimitives.dll
7FF944C40000 IMM32.DLL
