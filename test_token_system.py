#!/usr/bin/env python3
"""
Test script to verify the token-based resume tracking system
This script simulates multiple users uploading resumes and verifies they get their own back
"""

import requests
import time
import json
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Configuration
BASE_URL = "http://localhost:3001"  # Change to your server URL
TEST_FILES = [
    "test_resume_1.pdf",
    "test_resume_2.pdf", 
    "test_resume_3.pdf"
]

def create_test_file(filename, content):
    """Create a test PDF file with unique content"""
    with open(filename, 'w') as f:
        f.write(f"Test resume content for {filename}\nUnique ID: {uuid.uuid4()}\nContent: {content}")

def upload_resume(filename, user_id):
    """Upload a resume and return the tracking token"""
    print(f"User {user_id}: Uploading {filename}")
    
    try:
        with open(filename, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{BASE_URL}/upload", files=files)
            
        if response.status_code == 200:
            data = response.json()
            tracking_token = data.get('trackingToken')
            print(f"User {user_id}: Upload successful, token: {tracking_token}")
            return tracking_token, user_id, filename
        else:
            print(f"User {user_id}: Upload failed: {response.status_code} - {response.text}")
            return None, user_id, filename
            
    except Exception as e:
        print(f"User {user_id}: Upload error: {e}")
        return None, user_id, filename

def retrieve_resume(tracking_token, user_id, max_attempts=10):
    """Retrieve processed resume using tracking token"""
    print(f"User {user_id}: Polling for resume with token {tracking_token}")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{BASE_URL}/resume-by-token", params={'token': tracking_token})
            
            if response.status_code == 200:
                data = response.json()
                resume_name = data.get('basics', {}).get('name', 'Unknown')
                print(f"User {user_id}: ✅ Retrieved resume for: {resume_name}")
                return data, user_id, tracking_token
            elif response.status_code == 404:
                print(f"User {user_id}: Attempt {attempt + 1}: Resume still processing...")
                time.sleep(10)  # Wait 10 seconds before retry
            else:
                print(f"User {user_id}: Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"User {user_id}: Retrieval error: {e}")
            
    print(f"User {user_id}: ❌ Failed to retrieve resume after {max_attempts} attempts")
    return None, user_id, tracking_token

def test_concurrent_uploads():
    """Test multiple concurrent uploads to verify no cross-contamination"""
    print("🧪 Starting concurrent upload test...")
    
    # Create test files
    test_data = [
        ("test_resume_alice.txt", "Alice Johnson - Software Engineer"),
        ("test_resume_bob.txt", "Bob Smith - Data Scientist"), 
        ("test_resume_charlie.txt", "Charlie Brown - Product Manager")
    ]
    
    for filename, content in test_data:
        create_test_file(filename, content)
    
    # Upload files concurrently
    upload_results = []
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for i, (filename, content) in enumerate(test_data):
            future = executor.submit(upload_resume, filename, f"User{i+1}")
            futures.append(future)
        
        for future in as_completed(futures):
            result = future.result()
            if result[0]:  # If tracking token was received
                upload_results.append(result)
    
    print(f"\n📊 Upload Results: {len(upload_results)} successful uploads")
    
    # Wait a bit for processing to start
    print("⏳ Waiting 30 seconds for processing to begin...")
    time.sleep(30)
    
    # Retrieve resumes concurrently
    retrieval_results = []
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for tracking_token, user_id, filename in upload_results:
            future = executor.submit(retrieve_resume, tracking_token, user_id)
            futures.append(future)
        
        for future in as_completed(futures):
            result = future.result()
            if result[0]:  # If resume was retrieved
                retrieval_results.append(result)
    
    print(f"\n📊 Retrieval Results: {len(retrieval_results)} successful retrievals")
    
    # Verify no cross-contamination
    print("\n🔍 Verifying data integrity...")
    for data, user_id, tracking_token in retrieval_results:
        resume_name = data.get('basics', {}).get('name', 'Unknown')
        expected_names = {
            'User1': 'Alice Johnson',
            'User2': 'Bob Smith', 
            'User3': 'Charlie Brown'
        }
        
        expected_name = expected_names.get(user_id, 'Unknown')
        if expected_name in resume_name:
            print(f"✅ {user_id}: Correctly received own resume ({resume_name})")
        else:
            print(f"❌ {user_id}: CROSS-CONTAMINATION DETECTED! Expected {expected_name}, got {resume_name}")
    
    # Cleanup
    for filename, _ in test_data:
        try:
            import os
            os.remove(filename)
        except:
            pass

if __name__ == "__main__":
    print("🚀 Token-Based Resume Tracking System Test")
    print("=" * 50)
    test_concurrent_uploads()
    print("\n✅ Test completed!")
