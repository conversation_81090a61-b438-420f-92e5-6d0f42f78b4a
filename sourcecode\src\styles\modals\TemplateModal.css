.template-modal {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }

  .template-item {
    border: 1px solid #eaeaea;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
  }

  .template-item:hover {
    border-color: #4a6cf7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .template-item.selected {
    border-color: #4a6cf7;
    box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.3);
  }

  .template-preview {
    height: 200px;
    padding: 10px;
    background-color: #f9f9f9;
  }

  .template-thumbnail {
    width: 100%;
    height: 100%;
    background-color: white;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
  }

  .thumbnail-header {
    height: 30px;
    background-color: #f0f0f0;
    margin-bottom: 10px;
  }

  .thumbnail-content {
    flex: 1;
    display: flex;
  }

  .thumbnail-sidebar {
    width: 30%;
    background-color: #f0f0f0;
  }

  .thumbnail-main {
    flex: 1;
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .thumbnail-section {
    height: 20px;
    background-color: #f0f0f0;
  }

  .template-info {
    padding: 15px;
  }

  .template-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
  }

  .template-info p {
    font-size: 14px;
    color: #666;
  }

  .template-selected-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    background-color: #4a6cf7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  /* Template specific thumbnails */
  .template-thumbnail.modern .thumbnail-header {
    background-color: #4a6cf7;
  }

  .template-thumbnail.elegant {
    text-align: center;
  }

  .template-thumbnail.elegant .thumbnail-header {
    height: 40px;
  }

  .template-thumbnail.professional .thumbnail-header {
    background-color: #2c3e50;
  }

  .template-thumbnail.creative .thumbnail-sidebar {
    background-color: #4a6cf7;
  }
