/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.signup-modal {
  display: flex;
  width: 90%;
  max-width: 950px;
  max-height: 90vh;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Left Side - Branding */
.signup-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  position: relative;
  overflow: hidden;
}

.signup-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.signup-branding {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 400px;
  color: white;
}

.brand-icon {
  margin-bottom: 30px;
}

.star-icon {
  width: 80px;
  height: 80px;
  background: #4A90E2;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.signup-branding h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.signup-branding p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 40px;
  line-height: 1.5;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.15);
  padding: 15px 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 280px;
}

.feature-icon {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.feature-icon.blue {
  background: #4A90E2;
}

.feature-icon.green {
  background: #5CB85C;
}

.feature-icon.orange {
  background: #F0AD4E;
}

.feature-text h4 {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0;
  text-align: left;
}

/* Right Side - Sign Up Form */
.signup-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: white;
  position: relative;
}

.signup-form-container {
  width: 100%;
  max-width: 450px;
  position: relative;
}

.close-button {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  border: none;
  background: #f8f9fa;
  border-radius: 50%;
  font-size: 1.2rem;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #e9ecef;
  color: #495057;
}

.signup-header {
  text-align: center;
  margin-bottom: 30px;
}

.signup-header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

/* Social Sign Up Buttons */
.social-signup-section {
  display: flex;
  gap: 12px;
  margin-bottom: 25px;
  justify-content: center;
}

.social-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px 10px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.social-button:hover {
  border-color: #d1d5db;
  background: #f8f9fa;
}

.social-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.linkedin-icon {
  color: #0077b5;
}

.google-icon svg {
  width: 20px;
  height: 20px;
}

.facebook-icon {
  color: #4267b2;
}

.divider {
  text-align: center;
  margin: 25px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.divider span {
  background: white;
  padding: 0 15px;
  color: #6c757d;
  font-size: 0.85rem;
  position: relative;
}

/* Form Styling */
.signup-form {
  margin-bottom: 25px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
  margin-bottom: 20px;
}

.form-row .form-group {
  margin-bottom: 0;
}

.form-group input {
  width: 100%;
  padding: 15px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #f8f9fa;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #4A90E2;
  background: white;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input::placeholder {
  color: #9ca3af;
}

.signup-button {
  width: 100%;
  padding: 15px;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.signup-button:hover {
  background: #16a34a;
  transform: translateY(-1px);
}

/* Footer Links */
.signup-footer {
  text-align: center;
}

.signup-footer p {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.link-primary {
  color: #4A90E2;
  background: none;
  border: none;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0;
}

.link-primary:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .signup-container {
    flex-direction: column;
  }
  
  .signup-left {
    min-height: 40vh;
    padding: 30px 20px;
  }
  
  .signup-branding h2 {
    font-size: 1.8rem;
  }
  
  .features-list {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .feature-item {
    max-width: 140px;
    flex-direction: column;
    text-align: center;
    padding: 12px;
  }
  
  .feature-text h4 {
    font-size: 0.8rem;
    text-align: center;
  }
  
  .signup-right {
    padding: 30px 20px;
  }
  
  .social-signup-section {
    flex-direction: column;
  }
  
  .social-button {
    flex-direction: row;
    justify-content: center;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .form-row .form-group {
    margin-bottom: 20px;
  }
}
