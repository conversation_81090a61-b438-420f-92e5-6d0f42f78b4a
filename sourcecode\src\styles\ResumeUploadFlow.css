.resume-upload-flow-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.resume-upload-flow {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-number.active {
  background: #4A90E2;
  color: white;
}

.step-number.completed {
  background: #28a745;
  color: white;
}

.sign-in-btn {
  background: transparent;
  border: 1px solid #4A90E2;
  color: #4A90E2;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sign-in-btn:hover {
  background: #4A90E2;
  color: white;
}

.flow-content {
  padding: 40px;
  text-align: center;
}

.logo-section {
  margin-bottom: 30px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.flow-content h2 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
}

.upload-section {
  margin-bottom: 30px;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9ff;
  margin-bottom: 20px;
}

.file-upload-area:hover {
  border-color: #4A90E2;
  background: #f0f4ff;
}

.file-input {
  display: none;
}

.upload-label {
  cursor: pointer;
  display: block;
}

.upload-icon {
  margin-bottom: 15px;
}

.upload-label p {
  margin: 5px 0;
  color: #666;
}

.file-types {
  font-size: 12px;
  color: #999;
}

.uploaded-file {
  padding: 15px;
  background: #f0f8ff;
  border: 1px solid #4A90E2;
  border-radius: 8px;
  color: #4A90E2;
  font-weight: 500;
}

.job-description-section {
  text-align: left;
  margin-bottom: 30px;
}

.job-description-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.job-desc-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.job-description-textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
}

.job-description-textarea:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.upload-resume-btn {
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 16px 40px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
  min-width: 160px;
}

.upload-resume-btn:hover:not(:disabled) {
  background: #357ABD;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.upload-resume-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Processing Section */
.processing-section {
  text-align: center;
  padding: 40px 20px;
}

.processing-animation {
  margin: 30px 0;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-text {
  color: #666;
  font-style: italic;
}

@media (max-width: 768px) {
  .resume-upload-flow {
    width: 95%;
    margin: 20px;
  }
  
  .flow-content {
    padding: 30px 20px;
  }
  
  .flow-content h2 {
    font-size: 24px;
  }
  
  .file-upload-area {
    padding: 30px 15px;
  }
}
