/* Auth Modal Styles - Matching Screenshot Design */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.auth-modal-content {
  background: white;
  border-radius: 20px;
  width: 850px;
  height: 550px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.auth-modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
  z-index: 10;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.auth-modal-close:hover {
  background-color: #f5f5f5;
}

.auth-container {
  display: flex;
  height: 100%;
}

/* Left Side - Branding */
.auth-left-side {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 50px 35px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
  position: relative;
}

.auth-logo {
  margin-bottom: 30px;
}

.logo-icon {
  width: 55px;
  height: 55px;
  background: #007bff;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 18px;
}

.auth-left-side h2 {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 12px;
  color: white;
  text-align: center;
}

.auth-left-side p {
  font-size: 15px;
  opacity: 0.9;
  margin-bottom: 35px;
  line-height: 1.4;
  text-align: center;
}

.feature-icons {
  display: flex;
  gap: 18px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  transition: transform 0.2s ease;
}

.feature-icon:hover {
  transform: translateY(-1px);
}

.feature-icon.blue {
  background: #1e90ff;
  box-shadow: 0 2px 8px rgba(30, 144, 255, 0.3);
}

.feature-icon.green {
  background: #00c851;
  box-shadow: 0 2px 8px rgba(0, 200, 81, 0.3);
}

.feature-icon.orange {
  background: #ff8800;
  box-shadow: 0 2px 8px rgba(255, 136, 0, 0.3);
}

.feature-item span {
  font-size: 13px;
  font-weight: 500;
  line-height: 1.2;
}

/* Right Side - Form */
.auth-right-side {
  flex: 1;
  padding: 40px 35px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.auth-form-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 25px;
  text-align: center;
}

/* Social Login Buttons */
.social-login-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.social-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  color: #666;
  font-size: 11px;
  font-weight: 500;
}

.social-btn:hover {
  border-color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.social-btn svg {
  margin-bottom: 6px;
}

.social-btn.linkedin {
  color: #0077b5;
}

.social-btn.google {
  color: #4285f4;
}

.social-btn.facebook {
  color: #1877f2;
}

/* Divider */
.auth-divider {
  text-align: center;
  margin: 18px 0;
  position: relative;
  color: #999;
  font-size: 13px;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
  z-index: 1;
}

.auth-divider span {
  background: white;
  padding: 0 12px;
  position: relative;
  z-index: 2;
}

/* Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.auth-form input {
  padding: 12px 14px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  background: #f8f9fa;
}

.auth-form input:focus {
  outline: none;
  border-color: #007bff;
  background: white;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.auth-form input::placeholder {
  color: #999;
}

.auth-submit-btn {
  padding: 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 6px;
}

.auth-submit-btn:hover:not(:disabled) {
  background: #218838;
}

.auth-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Footer */
.auth-footer {
  text-align: center;
  margin-top: 18px;
  color: #666;
  font-size: 13px;
}

.auth-links {
  margin-top: 6px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.auth-link {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  text-decoration: none;
  font-size: 13px;
}

.auth-link:hover {
  text-decoration: underline;
}

/* Error Styles */
.auth-error {
  color: #dc3545;
  font-size: 12px;
  padding: 8px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin: 6px 0;
}

/* Verification Input Styles */
.verification-input {
  text-align: center;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-modal-content {
    width: 95%;
    height: auto;
    max-height: 90vh;
  }

  .auth-container {
    flex-direction: column;
  }

  .auth-left-side {
    padding: 30px 25px;
  }

  .auth-right-side {
    padding: 30px 25px;
  }

  .feature-icons {
    flex-direction: row;
    gap: 12px;
  }

  .social-login-buttons {
    flex-direction: column;
  }
}
