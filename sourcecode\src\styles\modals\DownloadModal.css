.download-modal {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .download-options {
    margin-top: 20px;
  }

  .download-options h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  .filename-input {
    margin-bottom: 24px;
  }

  .filename-input label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
  }

  .filename-field {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
  }

  .filename-field:focus {
    outline: none;
    border-color: #4a6cf7;
  }

  .format-toggle {
    display: flex;
    margin-top: 10px;
    margin-bottom: 15px;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #e0e0e0;
  }

  .format-toggle button {
    flex: 1;
    padding: 8px 12px;
    background: #f5f5f5;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .format-toggle button.active {
    background: #4a6cf7;
    color: white;
  }

  .format-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .format-option {
    border: 1px solid #eaeaea;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .format-option:hover {
    border-color: #4a6cf7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .format-option.selected {
    border-color: #4a6cf7;
    background-color: rgba(74, 108, 247, 0.05);
    box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.3);
  }

  .format-icon {
    margin-bottom: 12px;
    color: #4a6cf7;
  }

  .format-icon.pdf {
    color: #e74c3c;
  }

  .format-icon.docx {
    color: #3498db;
  }

  .format-icon.txt {
    color: #7f8c8d;
  }

  .format-icon.json {
    color: #2ecc71;
  }

  .format-option span {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 4px;
  }

  .format-option p {
    font-size: 12px;
    color: #666;
    margin: 0;
  }

  .format-option p.format-feature {
    font-size: 11px;
    color: #4a6cf7;
    margin-top: 5px;
    font-weight: 500;
  }

  .download-error {
    background-color: #ffebee;
    color: #c62828;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  .cancel-button {
    padding: 10px 16px;
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
  }

  .cancel-button:hover {
    background-color: #f5f5f5;
  }

  .download-button {
    padding: 10px 20px;
    background-color: #4a6cf7;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .download-button:hover {
    background-color: #3a5ce5;
  }

  .download-button:disabled {
    background-color: #4a6cf7;
    cursor: not-allowed;
    opacity: 0.9;
  }

  .download-button-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .download-button-loading span {
    font-size: 14px;
    white-space: nowrap;
  }

  .simple-progress-bar {
    width: 80px;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin-right: 8px;
  }

  .simple-progress-fill {
    height: 100%;
    background-color: white;
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  .simple-dots {
    position: relative;
  }

  .simple-dots:after {
    content: '';
    animation: dotAnimation 1.5s infinite;
  }

  @keyframes dotAnimation {
    0% { content: ''; }
    25% { content: '.'; }
    50% { content: '..'; }
    75% { content: '...'; }
    100% { content: ''; }
  }

  @media (max-width: 600px) {
    .format-options {
      grid-template-columns: 1fr;
    }
  }
