/* Auth.css - Shared styles for authentication components */

.auth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  max-width: 500px;
  margin: 40px auto;
  background-color: var(--color-background-alt);
  border-radius: 10px;
  box-shadow: var(--shadow-md);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 10px;
}

.auth-header p {
  font-size: 16px;
  color: var(--color-text-secondary);
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--color-text-primary);
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(58, 110, 165, 0.2);
}

.auth-button {
  width: 100%;
  padding: 12px;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.auth-button:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
}

.auth-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
}

.auth-links {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  font-size: 14px;
}

.auth-links a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.3s;
}

.auth-links a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: var(--color-text-light);
}

.auth-divider::before,
.auth-divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.auth-divider span {
  padding: 0 10px;
  font-size: 14px;
}

.error-message {
  color: #e53935;
  font-size: 14px;
  margin-top: 5px;
}

.success-message {
  color: #43a047;
  font-size: 14px;
  margin-top: 5px;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .auth-container {
    padding: 30px 15px;
    margin: 20px auto;
  }
  
  .auth-header h2 {
    font-size: 24px;
  }
}
