.resume-preview-container {
    width: 100%;
    overflow-x: auto;
    padding: 20px 0;
  }

  .pages-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .resume-page {
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    width: 793px; /* A4 width in pixels at 96dpi (210mm) */
    height: 1122px; /* A4 height in pixels at 96dpi (297mm) */
    margin-bottom: 40px;
    overflow: hidden;
    /* Scale to fit screen while maintaining A4 proportions */
    transform: scale(0.8);
    transform-origin: top center;
  }

  .page-number {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #f5f5f5;
    color: #666;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
  }

  .resume-preview {
    width: 100%;
    height: 100%;
    padding: 30px;
    background-color: #fff;
    position: relative;
    overflow: hidden;
    border-radius: 0;
  }

  .resume-content {
    width: 100%;
    position: absolute;
  }

  .resume-template {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
  }

  /* Add more styles for different templates as needed */
