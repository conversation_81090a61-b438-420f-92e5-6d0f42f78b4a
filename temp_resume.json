{"basics": {"name": "<PERSON>", "title": "AI Focused Software Developer", "email": "<EMAIL>", "phone": "03339167909", "location": "Islamabad, Pakistan", "summary": "Dedicated AI and software developer with extensive experience in web development, machine learning, and cloud computing. Proven track record in building scalable applications and optimizing processes using AI technologies. Adept at collaborating with cross-functional teams to deliver innovative solutions."}, "experience": [{"title": "Freelance AI Developer", "company": "Self-employed", "location": "Remote", "startDate": "2024-04", "endDate": "Present", "highlights": ["Managed web development projects with a focus on integrating AI solutions.", "Implemented SEO, ads, and marketing strategies to enhance website performance.", "Utilized AI algorithms to optimize user experience and engagement.", "Collaborated with clients to deliver tailored AI-driven web applications."]}, {"title": "AI Teaching Assistant", "company": "FAST University", "location": "Islamabad, Pakistan", "startDate": "2023-09", "endDate": "Present", "highlights": ["Assisted students in understanding complex AI and database concepts.", "Conducted lab demonstrations to showcase practical applications of AI.", "Developed educational materials to support AI curriculum.", "Mentored students on AI project development and research."]}, {"title": "AI-driven MERN Stack Developer", "company": "TechxServe", "location": "Remote", "startDate": "2024-08", "endDate": "2024-10", "highlights": ["Built a major B2B e-commerce platform using the MERN stack with AI integrations.", "Implemented machine learning models to enhance user recommendations.", "Developed real-time data processing pipelines using Node.js and MongoDB.", "Collaborated with cross-functional teams to deliver AI-enhanced features."]}, {"title": "React and Next.js AI Intern", "company": "Bytewise Limited", "location": "Remote", "startDate": "2024-06", "endDate": "2024-08", "highlights": ["Completed internship with a focus on developing AI-driven React applications.", "Integrated AI models for image and text recognition in web apps.", "Utilized Next.js to create server-side rendered pages with AI features.", "Collaborated with senior developers to implement AI-based user interfaces."]}], "education": [{"degree": "B.S Computer Science", "school": "FAST NUCES Islamabad", "location": "Islamabad, Pakistan", "graduationDate": "2025-06"}], "skills": [{"category": "Programming Languages", "items": ["C", "C++", "JAVA", "Python", "C#", "Assembly", "JavaFX"]}, {"category": "Web Development", "items": ["HTML", "SCSS", "JavaScript", "TypeScript", "Bootstrap", "React.js", "Next.js", "Node.js", "Express.js", "MongoDB", "PHP", "SQL", "Windows Forms", "<PERSON><PERSON><PERSON>", "Flutter", "RESTful APIs", "Authentication (JWT)", "Highchart"]}, {"category": "Databases", "items": ["SQL Server", "MySQL", "MongoDB", "Firebase"]}, {"category": "Programming Paradigms", "items": ["Object Oriented Programming (OOP)", "Data Structures"]}, {"category": "Artificial Intelligence", "items": ["PyTorch", "OpenCV", "TensorFlow", "Image Processing", "YOLO Model Fine-Tuning", "Foundation Models", "Sci-kit Learn", "<PERSON><PERSON>", "AWS"]}, {"category": "Version Control & Containerization", "items": ["Git", "GitHub", "<PERSON>er", "CI/CD Pipelines", "Kubernetes"]}, {"category": "Soft Skills", "items": ["Team Collaboration", "Problem Solving", "Project Management", "Client Communication", "Adaptability"]}], "projects": [{"name": "Empowered-AI", "description": "An AI-powered, voice-navigated app offering real-time object detection, scene description, text and currency recognition, item location, and emotion detection to support visually impaired users.", "technologies": ["Flutter", "PyTorch", "AWS", "TensorFlow", "Hugging Face", "YOLO v8", "TTS", "STT", "FastAPI"], "link": ""}, {"name": "TradXSell", "description": "A B2B e-commerce platform with modular support for buyers, sellers, admins, and quality assurance, enabling seamless trade and management workflows.", "technologies": ["MongoDB", "Express.js", "React Redux", "Node.js", "Tailwind", "<PERSON><PERSON>", "<PERSON><PERSON>"], "link": ""}, {"name": "DeliverWorx", "description": "A full-stack employee management system with check-in/out, live location tracking, and payment features.", "technologies": ["MongoDB", "Express.js", "Next.js", "Node.js", "Tailwind", "Cloudinary"], "link": ""}, {"name": "Resume Optimizer and Editor", "description": "A web app that allows users to upload resumes, automatically extracts and optimizes the data, and provides a custom-built editor for editing and downloading the final document.", "technologies": ["React", "Node", "S3", "Lambda", "Textract", "Bedrock"], "link": ""}, {"name": "Driving School Finder", "description": "A full-stack web app that helps users discover and compare driving schools across Germany by price, reviews, and services.", "technologies": ["Next.js", "Node.js", "Express", "MongoDB", "Cloudinary", "Tailwind"], "link": ""}], "certifications": [{"name": "AWS Academy Cloud Data Pipeline Builder", "issuer": "AWS", "date": "2024-06", "link": ""}, {"name": "AWS Academy Cloud Foundations", "issuer": "AWS", "date": "2024-06", "link": ""}, {"name": "AWS Academy Cloud Web Application Builder", "issuer": "AWS", "date": "2024-06", "link": ""}], "achievements": [], "languages": [{"language": "English", "proficiency": "Fluent"}], "volunteer": [], "publications": [], "interests": []}