<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATS Compatibility Check Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #4a6cf7;
        }
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        button:hover {
            background-color: #3a5bd9;
        }
        #result {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>ATS Compatibility Check Test</h1>
    <p>This page tests the ATS compatibility check endpoint.</p>
    
    <button id="checkButton">Check ATS Compatibility</button>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('checkButton').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('http://localhost:3001/ats-check?key=rewritten-resumes/004f4c78-7409-47ef-ba18-251088fb4fcd-MyCV.json');
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
