<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{name}} - Resume</title>
  <style>
    /* Global styles */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

    body {
      font-family: {{fontFamily}}, 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
      background-color: white;
      color: #333;
      line-height: 1.5;
      font-size: {{fontSize}}px;
    }

    .page {
      width: 210mm;
      min-height: 297mm;
      padding: 20mm;
      margin: 0 auto;
      background: white;
      position: relative;
      page-break-after: always;
    }

    /* Modern Template Styles */
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 2px solid {{primaryColor}};
    }

    .resume-header-content {
      flex: 1;
    }

    .resume-name-title {
      margin-bottom: 10px;
    }

    .resume-name {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 5px;
      line-height: 1.2;
      color: {{primaryColor}};
    }

    .resume-title {
      font-size: 16px;
      font-weight: 500;
      color: #555;
      margin-bottom: 5px;
    }

    .resume-contact {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }

    .resume-contact-item {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 12px;
    }

    .resume-section {
      margin-bottom: 15px;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin: 0;
      color: {{primaryColor}};
    }

    .section-content {
      font-size: 12px;
    }

    /* Experience section */
    .experience-item {
      margin-bottom: 15px;
    }

    .experience-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .experience-title-company {
      display: flex;
      flex-direction: column;
    }

    .experience-title {
      font-weight: 600;
      color: {{primaryColor}};
    }

    .experience-company {
      font-weight: 500;
    }

    .experience-period {
      color: #666;
    }

    .experience-description {
      margin-top: 5px;
    }

    .experience-bullets {
      margin-top: 5px;
      padding-left: 20px;
      list-style-type: disc;
    }

    .experience-bullet {
      margin-bottom: 3px;
    }

    /* Skills section */
    .skills-container {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }

    .skill-category {
      flex: 1;
      min-width: 200px;
    }

    .skill-category-name {
      font-weight: 600;
      margin-bottom: 5px;
      font-size: 13px;
      color: {{primaryColor}};
    }

    .skill-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .skill-item {
      background-color: #f5f5f5;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 11px;
    }

    /* Education section */
    .education-item {
      margin-bottom: 15px;
    }

    .education-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .education-degree-school {
      display: flex;
      flex-direction: column;
    }

    .education-degree {
      font-weight: 600;
      color: {{primaryColor}};
    }

    .education-school {
      font-weight: 500;
    }

    .education-year {
      color: #666;
    }

    /* Languages section */
    .languages-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .language-item {
      background-color: #f5f5f5;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 11px;
    }

    /* Certifications section */
    .certification-item {
      margin-bottom: 10px;
    }

    .certification-name {
      font-weight: 600;
      color: {{primaryColor}};
    }

    .certification-issuer {
      font-size: 11px;
    }

    /* Awards section */
    .award-item {
      margin-bottom: 15px;
    }

    .award-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .award-title {
      font-weight: 600;
      color: {{primaryColor}};
    }

    .award-date {
      color: #666;
    }

    .award-issuer {
      font-weight: 500;
    }

    .award-description {
      font-size: 11px;
      margin-top: 3px;
    }

    /* Summary section */
    .summary-content {
      font-size: 12px;
      line-height: 1.4;
    }
  </style>
</head>
<body>
  <div class="page">
    <div class="resume-template">
      {{{content}}}
    </div>
  </div>
</body>
</html>
