.loading-animation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 30px;
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.loading-text {
  margin-top: 20px;
  font-size: 18px;
  color: #4a6cf7;
  font-weight: 500;
}

/* Spinner Animation */
.spinner {
  width: 60px;
  height: 60px;
  position: relative;
}

.spinner-inner {
  width: 100%;
  height: 100%;
  border: 4px solid rgba(74, 108, 247, 0.2);
  border-top-color: #4a6cf7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Spinning Logo Animation */
.spinning-logo {
  width: 80px;
  height: 80px;
  animation: spin 2s linear infinite;
}

.spinning-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
}

/* Gears Animation */
.gears {
  position: relative;
  width: 80px;
  height: 80px;
}

.gear-large {
  position: absolute;
  width: 60px;
  height: 60px;
  background-color: #4a6cf7;
  border-radius: 50%;
  top: 0;
  left: 0;
  animation: spin 4s linear infinite;
}

.gear-large:before {
  content: '';
  position: absolute;
  width: 80%;
  height: 80%;
  background-color: white;
  border-radius: 50%;
  top: 10%;
  left: 10%;
}

.gear-small {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: #ff9800;
  border-radius: 50%;
  bottom: 0;
  right: 0;
  animation: spin-reverse 4s linear infinite;
}

.gear-small:before {
  content: '';
  position: absolute;
  width: 70%;
  height: 70%;
  background-color: white;
  border-radius: 50%;
  top: 15%;
  left: 15%;
}

@keyframes spin-reverse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

/* Dots Animation */
.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.loading-dots span {
  width: 12px;
  height: 12px;
  background-color: #4a6cf7;
  border-radius: 50%;
  display: inline-block;
  animation: dots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes dots {
  0%, 80%, 100% { 
    transform: scale(0);
  } 
  40% { 
    transform: scale(1.0);
  }
}

/* Progress Bar */
.progress-container {
  width: 250px;
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  margin-top: 15px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background-color: #4a6cf7;
  border-radius: 5px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 14px;
  color: #4a6cf7;
}
