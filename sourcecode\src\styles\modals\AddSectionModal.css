.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .add-section-modal {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .modal-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
  
  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 50%;
    transition: background-color 0.2s;
  }
  
  .close-button:hover {
    background-color: #f0f0f0;
  }
  
  .modal-subtitle {
    color: #666;
    margin-bottom: 24px;
  }
  
  .section-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
  }
  
  .section-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .section-item:hover {
    border-color: #4a6cf7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .section-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    margin-bottom: 12px;
    color: #4a6cf7;
  }
  
  .section-name {
    font-weight: 500;
    text-align: center;
  }
  
  .custom-section-input {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .custom-section-input-field {
    padding: 12px;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.2s;
  }
  
  .custom-section-input-field:focus {
    border-color: #4a6cf7;
  }
  
  .custom-section-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  
  .cancel-button {
    padding: 10px 16px;
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .cancel-button:hover {
    background-color: #f5f5f5;
  }
  
  .add-button {
    padding: 10px 20px;
    background-color: #4a6cf7;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .add-button:hover {
    background-color: #3a5ce5;
  }
  