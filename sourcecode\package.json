{"name": "chambers-v", "version": "0.1.0", "private": true, "dependencies": {"@aws-amplify/ui-react": "^6.11.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "amazon-cognito-identity-js": "^6.3.15", "aws-amplify": "^6.14.4", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "docx": "^9.4.1", "dotenv": "^16.5.0", "framer-motion": "^11.0.8", "html-docx-js": "^0.3.1", "motion-dom": "^12.12.1", "oidc-client-ts": "^3.3.0", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-oidc-context": "^3.3.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "requests": "^0.3.0", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}