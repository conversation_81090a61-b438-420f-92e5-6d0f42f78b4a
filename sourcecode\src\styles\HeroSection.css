.hero-section {
  padding: 0;
  margin: 0;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

/* Video Background */
.hero-video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

/* Video Overlay */
.hero-video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

.hero-content-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 40px;
  position: relative;
  z-index: 2;
  padding: 120px 20px 40px;
  text-align: center;
  min-height: 100vh;
}

.hero-text-content {
  flex: 1;
  max-width: 800px;
  text-align: center;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hero-text-content h1 {
  font-size: 4rem;
  margin-bottom: 20px;
  color: #ffffff;
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -1px;
  position: relative;
  z-index: 1;
  text-align: center;
  width: 100%;
}

.hero-text-content h1 .highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  display: inline-block;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #ffffff;
  margin-bottom: 40px;
  line-height: 1.5;
  font-weight: 300;
  position: relative;
  z-index: 1;
  text-align: center;
  width: 100%;
  max-width: 600px;
}

/* CTA Section */
.hero-cta-section {
  margin-bottom: 40px;
  z-index: 2;
  position: relative;
}

.hero-primary-button {
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 140px;
}

.hero-primary-button:hover {
  background: #357ABD;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.hero-primary-button::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #4A90E2, transparent);
  border-radius: 2px;
  opacity: 0.7;
}

.hero-image-content {
  flex: 1;
  max-width: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
  overflow: visible;
}

.hero-dashboard-image {
  width: 100%;
  height: auto;
  transition: all 0.5s ease;
  filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.3));
  margin-left: 0;
  max-width: 400px;
  border-radius: 12px;
}

.hero-dashboard-image:hover {
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.4));
  transform: scale(1.02);
}

.hero-features {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.hero-feature {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 10px 16px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: 500;
}

.hero-feature:hover {
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.feature-icon {
  color: #4A90E2;
  font-weight: bold;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background-color: rgba(74, 144, 226, 0.2);
  border-radius: 50%;
}

@media (max-width: 992px) {
  .hero-section {
    padding: 60px 20px;
    min-height: 90vh;
  }

  .hero-content-wrapper {
    gap: 30px;
  }

  .hero-text-content h1 {
    font-size: 3.2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 35px;
  }

  .hero-dashboard-image {
    max-width: 350px;
  }

  .hero-features {
    gap: 15px;
  }

  .hero-feature {
    font-size: 0.9rem;
    padding: 8px 14px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 40px 20px;
    min-height: 80vh;
  }

  .hero-text-content h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 30px;
  }

  .hero-primary-button {
    padding: 14px 28px;
    font-size: 1rem;
  }

  .hero-dashboard-image {
    max-width: 300px;
  }

  .hero-features {
    flex-direction: column;
    gap: 12px;
  }

  .hero-feature {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 30px 15px;
    min-height: 70vh;
  }

  .hero-text-content h1 {
    font-size: 2rem;
    line-height: 1.3;
  }

  .hero-subtitle {
    font-size: 0.95rem;
    margin-bottom: 25px;
  }

  .hero-primary-button {
    padding: 12px 24px;
    font-size: 0.95rem;
    width: 100%;
    max-width: 200px;
  }

  .hero-dashboard-image {
    max-width: 250px;
  }

  .hero-features {
    gap: 10px;
  }

  .hero-feature {
    font-size: 0.85rem;
    padding: 8px 12px;
  }
}
