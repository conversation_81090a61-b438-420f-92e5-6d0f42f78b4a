.editor-container {
  display: flex;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

.editor-sidebar-wrapper {
  position: sticky;
  top: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin-left: 60px; /* Account for collapsed dashboard sidebar width */
}



.main-content-area {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.resume-preview-container {
  flex: 1;
  padding: 20px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.ats-check-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

/* Resume preview styles moved to ResumePreview.css */

/* Resume Template Styles */
.resume-template {
  height: 100%;
}

/* Modern Template */
.modern-template .resume-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.modern-template .resume-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modern-template .resume-name {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  color: #222;
}

.modern-template .resume-title {
  font-size: 18px;
  color: #555;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.modern-template .resume-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  border: 3px solid #fff;
}

.modern-template .resume-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modern-template .resume-contact {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 15px;
}

.modern-template .contact-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #444;
}

.modern-template .contact-icon {
  margin-right: 8px;
  color: #4a6cf7;
}

.modern-template .resume-section {
  margin-bottom: 20px;
  position: relative;
}

.modern-template .resume-section:last-child {
  margin-bottom: 0;
}

.modern-template .section-title {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding-bottom: 5px;
  border-bottom: 2px solid #4a6cf7;
  display: inline-block;
}

.modern-template .experience-item,
.modern-template .education-item,
.modern-template .project-item,
.modern-template .certification-item,
.modern-template .language-item,
.modern-template .reference-item,
.modern-template .achievement-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.modern-template .experience-item:last-child,
.modern-template .education-item:last-child,
.modern-template .project-item:last-child,
.modern-template .certification-item:last-child,
.modern-template .language-item:last-child,
.modern-template .reference-item:last-child,
.modern-template .achievement-item:last-child {
  border-bottom: none;
}

/* Achievement styles */
.modern-template .achievement-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.modern-template .achievement-title {
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.3px;
}

.modern-template .achievement-details {
  text-align: right;
  color: #666;
}

.modern-template .achievement-organization {
  font-weight: 500;
  color: #555;
}

.modern-template .achievement-date {
  font-style: italic;
  font-size: 13px;
  color: #777;
}

.modern-template .achievement-description {
  margin-top: 5px;
  color: #444;
}

.modern-template .experience-header,
.modern-template .education-header,
.modern-template .project-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.modern-template .experience-title,
.modern-template .education-degree,
.modern-template .project-name,
.modern-template .certification-name,
.modern-template .language-name,
.modern-template .reference-name {
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.3px;
}

.modern-template .experience-company,
.modern-template .education-school {
  font-weight: 500;
  margin-top: 4px;
  color: #555;
}

.modern-template .experience-period,
.modern-template .education-period {
  font-style: italic;
  color: #777;
  font-size: 13px;
}

.modern-template .experience-bullets {
  padding-left: 20px;
  margin-top: 10px;
  list-style-type: disc;
}

.modern-template .experience-bullets li {
  margin-bottom: 6px;
  position: relative;
  line-height: 1.4;
}

.modern-template .skills-container,
.modern-template .interests-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.modern-template .skill-category {
  margin-bottom: 10px;
}

.modern-template .skill-category-name {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 5px;
  padding-bottom: 3px;
  border-bottom: 1px solid #eaeaea;
}

.modern-template .skill-items {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.modern-template .skill-item,
.modern-template .interest-item {
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 13px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.modern-template .skill-item:hover {
  background-color: #e8e8e8;
  transform: translateY(-2px);
  box-shadow: 0 3px 5px rgba(0,0,0,0.1);
}

.modern-template .project-item {
  margin-bottom: 12px;
}

.modern-template .project-header {
  margin-bottom: 4px;
}

.modern-template .project-name {
  font-weight: 600;
  font-size: 1rem;
}

.modern-template .project-description {
  margin: 5px 0;
  font-size: 0.9rem;
  line-height: 1.3;
}

.modern-template .project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 4px;
}

.modern-template .project-tech {
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8em;
  color: #555;
}

.modern-template .reference-details {
  margin-top: 5px;
  color: #666;
}

/* Certification section styles */
.modern-template .certification-item {
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}

.modern-template .certification-header {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.modern-template .certification-name {
  font-weight: 600;
  font-size: 0.95rem;
  margin-right: 8px;
}

.modern-template .certification-date {
  font-size: 0.85rem;
  color: #666;
}

.modern-template .certification-issuer {
  font-style: italic;
  font-size: 0.9rem;
  color: #555;
}

/* Achievement section styles */
.modern-template .achievement-item {
  margin-bottom: 15px;
}

.modern-template .achievement-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 5px;
}

.modern-template .achievement-title {
  font-weight: bold;
  font-size: 1.1rem;
}

.modern-template .achievement-date {
  font-size: 0.9rem;
  color: #666;
}

.modern-template .achievement-organization {
  font-style: italic;
  margin-bottom: 5px;
}

.modern-template .achievement-description {
  margin-top: 5px;
  line-height: 1.4;
}

/* Publication section styles */
.modern-template .publication-item {
  margin-bottom: 15px;
}

.modern-template .publication-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 5px;
}

.modern-template .publication-title {
  font-weight: bold;
  font-size: 1.1rem;
}

.modern-template .publication-date {
  font-size: 0.9rem;
  color: #666;
}

.modern-template .publication-publisher {
  font-style: italic;
  margin-bottom: 5px;
}

.modern-template .publication-link {
  font-size: 0.9rem;
  color: #0066cc;
  text-decoration: underline;
}

/* Volunteer section styles */
.modern-template .volunteer-item {
  margin-bottom: 15px;
}

.modern-template .volunteer-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 5px;
}

.modern-template .volunteer-role-org {
  display: flex;
  flex-direction: column;
}

.modern-template .volunteer-role {
  font-weight: bold;
  font-size: 1.1rem;
}

.modern-template .volunteer-organization {
  font-style: italic;
}

.modern-template .volunteer-period {
  font-size: 0.9rem;
  color: #666;
}

.modern-template .volunteer-description {
  margin-top: 5px;
  line-height: 1.4;
}

/* Elegant Template */
.elegant-template .resume-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
}

.elegant-template .resume-name {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 5px;
}

.elegant-template .resume-title {
  font-size: 18px;
  color: #666;
}

.elegant-template .resume-contact {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.elegant-template .resume-contact-item,
.elegant-template .contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.elegant-template .section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
}

.elegant-template .experience-item,
.elegant-template .education-item {
  margin-bottom: 25px;
}

.elegant-template .experience-title,
.elegant-template .education-degree {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 5px;
}

.elegant-template .experience-company-location,
.elegant-template .education-school-location {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.elegant-template .separator {
  margin: 0 10px;
  color: #999;
}

.elegant-template .experience-period,
.elegant-template .education-year {
  color: #666;
  font-style: italic;
  margin-bottom: 10px;
}

.elegant-template .skills-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.elegant-template .skill-category {
  margin-bottom: 15px;
}

.elegant-template .skill-category-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  color: #444;
  letter-spacing: 0.5px;
}

.elegant-template .skill-items {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.elegant-template .skill-item {
  font-size: 15px;
  padding: 5px 12px;
  background-color: #f8f8f8;
  border-radius: 3px;
  border-left: 3px solid #4a6cf7;
}

/* Professional Template */
.professional-template .resume-header {
  padding: 30px;
  margin-bottom: 20px;
  text-align: center;
}

.professional-template .resume-name {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 5px;
}

.professional-template .resume-title {
  font-size: 18px;
}

.professional-template .resume-contact-bar {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 10px 0;
  margin-bottom: 30px;
  background-color: #f5f5f5;
}

.professional-template .resume-contact-item,
.professional-template .contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.professional-template .section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.professional-template .section-content {
  margin-bottom: 30px;
}

.professional-template .experience-item,
.professional-template .education-item {
  margin-bottom: 20px;
}

.professional-template .experience-header,
.professional-template .education-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.professional-template .experience-title-company,
.professional-template .education-degree-school {
  display: flex;
  flex-direction: column;
}

.professional-template .experience-title,
.professional-template .education-degree {
  font-weight: 600;
}

.professional-template .experience-location-period,
.professional-template .education-location-year {
  text-align: right;
  color: #666;
}

.professional-template .skills-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.professional-template .skill-category {
  margin-bottom: 20px;
  background-color: #fafafa;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.professional-template .skill-category-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.professional-template .skill-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.professional-template .skill-item {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.3px;
}



/* Creative Template */
.creative-template {
  display: flex;
}

.creative-template .resume-sidebar {
  width: 30%;
  padding: 30px 20px;
  color: white;
}

.creative-template .sidebar-photo {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 30px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
}

.creative-template .sidebar-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.creative-template .sidebar-section {
  margin-bottom: 30px;
}

.creative-template .sidebar-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 5px;
}

.creative-template .contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.creative-template .contact-icon {
  margin-right: 10px;
}

.creative-template .skills-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.creative-template .skill-category {
  margin-bottom: 15px;
}

.creative-template .skill-category-name {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.creative-template .skill-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.creative-template .skill-item {
  font-size: 14px;
  padding: 5px 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.creative-template .skill-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.creative-template .resume-main {
  width: 70%;
  padding: 30px;
}

.creative-template .resume-header {
  margin-bottom: 30px;
}

.creative-template .resume-name {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 5px;
}

.creative-template .resume-title {
  font-size: 18px;
  color: #666;
}

.creative-template .resume-section {
  margin-bottom: 30px;
}

.creative-template .section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.creative-template .experience-item,
.creative-template .education-item {
  margin-bottom: 20px;
}

.creative-template .experience-title,
.creative-template .education-degree {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 5px;
}

.creative-template .experience-subheader,
.creative-template .education-subheader {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 10px;
  color: #666;
  font-size: 14px;
}

.creative-template .experience-separator,
.creative-template .education-separator {
  margin: 0 8px;
  color: #ccc;
}

.creative-template .experience-bullets {
  padding-left: 20px;
}

.delete-item-btn,
.delete-bullet-btn {
  position: absolute;
  right: -25px;
  top: 5px;
  background-color: #fee2e2;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #dc2626;
  z-index: 5;
  transition: all 0.2s ease;
}

.delete-bullet-btn {
  right: -22px;
  top: 2px;
  width: 16px;
  height: 16px;
}

.delete-item-btn:hover,
.delete-bullet-btn:hover {
  background-color: #dc2626;
  color: white;
  transform: scale(1.1);
}

.section-title span {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.section-title span:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.section-title input {
  font: inherit;
  color: inherit;
  background: none;
  border: 1px solid #4a6cf7;
  border-radius: 3px;
  padding: 2px 4px;
  width: 100%;
  outline: none;
}

.section-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Add these styles to the existing Editor.css file */

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #4a6cf7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #fee2e2;
  color: #dc2626;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 400px;
}

.error-notification p {
  margin: 0;
  margin-right: 15px;
}

.error-notification button {
  background-color: #dc2626;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.error-notification button:hover {
  background-color: #b91c1c;
}

/* Add this to the existing Editor.css file */

.aws-status-notification {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  z-index: 1100;
  max-width: 500px;
  width: 90%;
}

.aws-status-notification h3 {
  margin-top: 0;
  color: #dc2626;
}

.aws-status-notification ul {
  margin: 10px 0;
  padding-left: 20px;
}

.aws-status-notification button {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.aws-status-notification button:hover {
  background-color: #3a5ce5;
}

/* Add this to the existing Editor.css file */

.s3-actions {
  position: fixed;
  top: 90px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 900;
}

.action-button {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  background-color: #3a5ce5;
}

.action-button:disabled {
  background-color: #c5c5c5;
  cursor: not-allowed;
}

/* Section Header Styles for All Templates */
.section-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Auto-save indicator styles */
.auto-save-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: all 0.3s ease;
}

.auto-save-indicator.saving {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.auto-save-indicator.saved {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.auto-save-indicator.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

.saving-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #fbbf24;
  border-top: 2px solid #92400e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
