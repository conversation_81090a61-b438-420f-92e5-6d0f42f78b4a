import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/ResumeUploadFlow.css';

const ResumeUploadFlow = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const [uploadedFile, setUploadedFile] = useState(null);
  const [jobDescription, setJobDescription] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleUploadResume = () => {
    if (!uploadedFile || !jobDescription.trim()) {
      alert('Please upload a resume and add a job description');
      return;
    }

    setIsProcessing(true);

    // Simulate processing
    setTimeout(() => {
      // Mock processed data
      const mockProcessedData = {
        name: "John Doe",
        title: "Software Engineer",
        contact: {
          phone: "+****************",
          email: "<EMAIL>",
          linkedin: "linkedin.com/in/johndoe",
        },
        summary: "Experienced software engineer with expertise in full-stack development...",
        experience: [],
        education: [],
        skills: []
      };

      sessionStorage.setItem('currentResumeData', JSON.stringify(mockProcessedData));
      setIsProcessing(false);
      onClose();
      navigate('/editor');
    }, 3000);
  };

  return (
    <div className="resume-upload-flow-overlay">
      <div className="resume-upload-flow">
        <div className="step-indicator">
          <div className="step-number completed">1</div>
          <div className="step-number active">2</div>
          <div className="step-number">3</div>
          <button className="sign-in-btn">Sign In</button>
        </div>

        <div className="flow-content">
          {isProcessing ? (
            <div className="processing-section">
              <div className="logo-section">
                <div className="logo-icon">
                  <svg width="40" height="40" viewBox="0 0 24 24" fill="white">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
              </div>
              <h2>Processing Your Resume</h2>
              <p>Our AI is optimizing your resume for the job description</p>
              <div className="processing-animation">
                <div className="spinner"></div>
              </div>
              <p className="processing-text">This may take a few moments...</p>
            </div>
          ) : (
            <>
              <div className="logo-section">
                <div className="logo-icon">
                  <svg width="40" height="40" viewBox="0 0 24 24" fill="white">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
              </div>

              <h2>Upload Your Resume</h2>
              <p className="subtitle">Let our AI optimize it for your target job</p>

              <div className="upload-section">
                <div 
                  className="file-upload-area"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                    className="file-input"
                    id="resume-upload"
                  />
                  <label htmlFor="resume-upload" className="upload-label">
                    <div className="upload-icon">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="#4A90E2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                        <polyline points="14,2 14,8 20,8"/>
                      </svg>
                    </div>
                    <p>Drag & drop your resume here</p>
                    <p className="file-types">or click to browse files (PDF, DOC, DOCX)</p>
                  </label>
                </div>

                {uploadedFile && (
                  <div className="uploaded-file">
                    <p>✓ {uploadedFile.name} uploaded successfully</p>
                  </div>
                )}
              </div>

              <div className="job-description-section">
                <h3>Job Description</h3>
                <p className="job-desc-subtitle">Add a job description to tailor your resume for a specific position</p>
                <textarea
                  className="job-description-textarea"
                  placeholder="Paste the job description here..."
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  rows={6}
                />
              </div>

              <button
                className="upload-resume-btn"
                onClick={handleUploadResume}
                disabled={!uploadedFile || !jobDescription.trim()}
              >
                Upload Resume
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResumeUploadFlow;
