import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import '../styles/Dashboard.css';
import DashboardSidebar from './DashboardSidebar';
import ResumeUploadModal from './ResumeUploadModal';
// Resume Preview Component
const ResumePreview = ({ resume, onClick }) => {
  const [resumeData, setResumeData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchResumeData = async () => {
      try {
        const response = await fetch(`http://localhost:3001/resume-by-token?token=${resume.trackingToken}`);
        if (response.ok) {
          const data = await response.json();
          setResumeData(data);
        }
      } catch (error) {
        console.log("Could not fetch resume data for preview:", error);
      } finally {
        setLoading(false);
      }
    };

    if (resume.trackingToken && !resume.id.startsWith('mock-')) {
      fetchResumeData();
    } else {
      setLoading(false);
    }
  }, [resume.trackingToken, resume.id]);

  if (loading) {
    return (
      <div className="resume-preview-loading" onClick={onClick}>
        <div className="loading-placeholder">
          <div className="loading-line"></div>
          <div className="loading-line short"></div>
          <div className="loading-line"></div>
          <div className="loading-line short"></div>
        </div>
      </div>
    );
  }

  const basics = resumeData?.basics || {};
  const experience = resumeData?.experience || [];
  const education = resumeData?.education || [];
  const skills = resumeData?.skills || [];

  // Helper function to truncate long names
  const truncateName = (name) => {
    if (!name) return 'Resume';
    if (name.length <= 20) return name;
    return name.substring(0, 18) + '...';
  };

  return (
    <div className="resume-preview-thumbnail" onClick={onClick}>
      <div className="resume-page">
        {/* Header */}
        <div className="resume-header">
          <div className="resume-name">{truncateName(basics.name || resume.title)}</div>
          <div className="resume-title">{basics.title || 'Professional'}</div>
          <div className="resume-contact">
            {basics.email && <div className="contact-item">{basics.email.substring(0, 20)}</div>}
            {basics.location && <div className="contact-item">{basics.location.substring(0, 15)}</div>}
          </div>
        </div>

        {/* Summary */}
        {basics.summary && (
          <div className="resume-section">
            <div className="section-title">SUMMARY</div>
            <div className="section-content">
              <div className="text-line">{basics.summary.substring(0, 45)}...</div>
            </div>
          </div>
        )}

        {/* Experience */}
        {experience.length > 0 && (
          <div className="resume-section">
            <div className="section-title">EXPERIENCE</div>
            <div className="section-content">
              {experience.slice(0, 1).map((exp, index) => (
                <div key={index} className="experience-item">
                  <div className="exp-title">{(exp.position || 'Position').substring(0, 18)}</div>
                  <div className="exp-company">{(exp.company || 'Company').substring(0, 18)}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education */}
        {education.length > 0 && (
          <div className="resume-section">
            <div className="section-title">EDUCATION</div>
            <div className="section-content">
              {education.slice(0, 1).map((edu, index) => (
                <div key={index} className="education-item">
                  <div className="edu-degree">{(edu.degree || 'Degree').substring(0, 18)}</div>
                  <div className="edu-school">{(edu.institution || 'Institution').substring(0, 18)}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills */}
        {skills.length > 0 && (
          <div className="resume-section">
            <div className="section-title">SKILLS</div>
            <div className="section-content">
              <div className="skills-list">
                {skills.slice(0, 4).join(' • ')}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const Dashboard = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [userResumes, setUserResumes] = useState([]);
  const [userStats, setUserStats] = useState({
    totalResumes: 0,
    totalCoverLetters: 0,
    totalTemplates: 0
  });
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check for create modal trigger from navigation
  useEffect(() => {
    if (location.state?.openCreateModal) {
      setIsUploadModalOpen(true);
      // Clear the state to prevent reopening on refresh
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Load user's resumes from S3
  useEffect(() => {
    const loadUserResumes = async () => {
      if (!auth.user?.profile?.email) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        console.log("Loading resumes for user:", auth.user.profile.email);

        console.log("🔍 Loading resumes for user:", auth.user.profile.email);

        let resumes = [];

        try {
          // Try to fetch real resumes from S3 for this specific user
          console.log("📡 Attempting to fetch real resumes from S3 for user:", auth.user.profile.email);
          const url = `http://localhost:3001/list-rewritten-resumes?user_email=${encodeURIComponent(auth.user.profile.email)}`;
          console.log("🔗 Request URL:", url);
          const response = await fetch(url);

          if (response.ok) {
            const allResumes = await response.json();
            console.log("📋 Found resumes in S3:", allResumes.length);

            // Convert S3 file list to resume format and filter by user
            // For now, show only the most recent 5 resumes as user-specific
            // TODO: Implement proper user filtering when user metadata is available
            const recentResumes = allResumes
              .sort((a, b) => new Date(b.LastModified) - new Date(a.LastModified))
              .slice(0, 5); // Show only 5 most recent resumes as "user's resumes"

            resumes = recentResumes.map((file, index) => {
              const fileName = file.Key.split('/').pop().replace('.json', '');
              const parts = fileName.split('-');
              const trackingToken = parts[0];
              const originalName = parts.slice(1).join('-') || 'Resume';

              return {
                id: trackingToken,
                title: originalName.replace(/\.(pdf|doc|docx)$/i, ''),
                name: 'User', // Will be updated when we load actual content
                lastModified: file.LastModified,
                createdAt: new Date(file.LastModified).getTime() / 1000,
                size: file.Size,
                key: file.Key,
                trackingToken: trackingToken,
                originalFilename: originalName
              };
            });

            // Sort by creation time (most recent first)
            resumes.sort((a, b) => b.createdAt - a.createdAt);

            console.log("✅ Real resumes loaded:", resumes.length);
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        } catch (fetchError) {
          console.warn("⚠️ Could not fetch real resumes, using mock data:", fetchError.message);

          // Fallback to mock data if real data fails
          resumes = [
            {
              id: 'mock-1',
              title: 'Software Engineer Resume',
              name: 'John Doe',
              lastModified: new Date().toISOString(),
              createdAt: Math.floor(Date.now() / 1000) - 86400,
              size: 1024,
              key: 'mock-key-1',
              trackingToken: 'mock-token-1',
              originalFilename: 'software-engineer-resume.pdf'
            },
            {
              id: 'mock-2',
              title: 'Product Manager Resume',
              name: 'Jane Smith',
              lastModified: new Date(Date.now() - 172800000).toISOString(),
              createdAt: Math.floor(Date.now() / 1000) - 172800,
              size: 2048,
              key: 'mock-key-2',
              trackingToken: 'mock-token-2',
              originalFilename: 'product-manager-resume.pdf'
            }
          ];
        }

        console.log("📊 Final resumes to display:", resumes.length);
        setUserResumes(resumes);

        // Calculate user stats based on actual data
        setUserStats({
          totalResumes: resumes.length,
          totalCoverLetters: 0, // Will be implemented later
          totalTemplates: 5 // Default templates available
        });

      } catch (error) {
        console.error("Error loading user resumes:", error);
        setError("Failed to load your resumes. Please try again.");
        setUserResumes([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserResumes();
  }, [auth.user]);

  // Refresh resumes when user navigates to dashboard
  useEffect(() => {
    if (location.pathname === '/dashboard' && auth.user?.profile?.email) {
      refreshResumes();
    }
  }, [location.pathname, auth.user]);

  const getUserDisplayName = () => {
    if (auth.user?.profile?.name) return auth.user.profile.name;
    if (auth.user?.profile?.email) return auth.user.profile.email.split('@')[0];
    return 'User';
  };

  const getUserEmail = () => {
    return auth.user?.profile?.email || '<EMAIL>';
  };

  // Handle resume editing
  const handleEditResume = async (resume) => {
    try {
      console.log("🎯 Opening resume for editing:", resume);
      console.log("🔍 Resume details:", {
        id: resume.id,
        title: resume.title,
        trackingToken: resume.trackingToken,
        key: resume.key
      });

      setError(null); // Clear any previous errors

      // Show loading state
      setIsLoading(true);

      // For mock data, create a sample resume structure
      if (resume.id.startsWith('mock-')) {
        console.log("📝 Loading mock resume data for:", resume.title);
        const mockResumeData = {
          basics: {
            name: resume.name,
            title: resume.title,
            email: "<EMAIL>",
            phone: "+****************",
            location: "San Francisco, CA",
            linkedin: "linkedin.com/in/user",
            summary: "Experienced professional with expertise in modern technologies and best practices."
          },
          experience: [
            {
              company: "Tech Company",
              position: "Senior Developer",
              startDate: "2022-01",
              endDate: "Present",
              description: "Led development of key features and mentored junior developers."
            }
          ],
          education: [
            {
              institution: "University",
              degree: "Bachelor of Science",
              field: "Computer Science",
              graduationDate: "2020"
            }
          ],
          skills: ["JavaScript", "React", "Node.js", "Python", "AWS"]
        };

        // Store the mock data in sessionStorage for the editor
        sessionStorage.setItem('currentResumeData', JSON.stringify(mockResumeData));
        sessionStorage.setItem('currentResumeId', resume.id);
        sessionStorage.setItem('editMode', 'true');

        console.log("✅ Mock resume data stored, navigating to editor");
        setIsLoading(false);

        // Navigate to editor
        navigate('/editor');
        return;
      }

      // Simplified approach: Always try to fetch real data, but have a reliable fallback
      console.log("🔍 Fetching resume data with token:", resume.trackingToken);

      let resumeData = null;
      let dataSource = "unknown";

      // Method 1: Try token-based fetch
      try {
        console.log("📡 Attempting token-based fetch...");
        const response = await fetch(`http://localhost:3001/resume-by-token?token=${resume.trackingToken}`);

        if (response.ok) {
          resumeData = await response.json();
          dataSource = "token-based";
          console.log("✅ Token-based fetch successful");
        } else {
          console.warn(`⚠️ Token-based fetch failed: ${response.status}`);
        }
      } catch (tokenError) {
        console.warn("⚠️ Token-based fetch error:", tokenError.message);
      }

      // Method 2: If token fetch failed, create a working fallback
      if (!resumeData) {
        console.log("🔄 Creating fallback resume structure...");
        resumeData = {
          basics: {
            name: resume.title.includes('Abdul') ? "Abdul Wasay" : "Professional",
            title: resume.title || "Professional Resume",
            email: "<EMAIL>",
            phone: "+****************",
            location: "City, Country",
            linkedin: "linkedin.com/in/user",
            summary: `Professional with experience in ${resume.title}. This resume was loaded from: ${resume.originalFilename}`
          },
          experience: [
            {
              company: "Current Company",
              position: resume.title || "Professional",
              startDate: "2020-01",
              endDate: "Present",
              description: "Professional experience and achievements in the field."
            },
            {
              company: "Previous Company",
              position: "Junior Role",
              startDate: "2018-01",
              endDate: "2019-12",
              description: "Earlier professional experience and learning."
            }
          ],
          education: [
            {
              institution: "University",
              degree: "Bachelor's Degree",
              field: "Relevant Field",
              graduationDate: "2018"
            }
          ],
          skills: ["Professional Skills", "Technical Skills", "Communication", "Leadership"]
        };
        dataSource = "fallback";
        console.log("✅ Fallback structure created");
      }

      // Store data and navigate
      console.log(`💾 Storing resume data (source: ${dataSource})`);
      sessionStorage.setItem('currentResumeData', JSON.stringify(resumeData));
      sessionStorage.setItem('currentResumeId', resume.id);
      sessionStorage.setItem('editMode', 'true');

      setIsLoading(false);

      console.log("🚀 Navigating to editor...");
      navigate('/editor');

      if (dataSource === "fallback") {
        setError("Loaded with template data. You can edit and save your changes.");
      }

    } catch (error) {
      console.error("💥 Error in handleEditResume:", error);
      setIsLoading(false);
      setError("Failed to open resume for editing. Please try again.");
    }
  };

  // Handle resume deletion
  const handleDeleteResume = async (resume) => {
    try {
      console.log("🗑️ Deleting resume:", resume);

      // Show confirmation dialog
      const confirmDelete = window.confirm(
        `Are you sure you want to delete "${resume.title}"?\n\nThis action cannot be undone and will remove the resume from AWS S3 storage.`
      );

      if (!confirmDelete) {
        console.log("❌ Delete cancelled by user");
        return;
      }

      setIsLoading(true);
      setError(null);

      // Try to delete from S3
      try {
        console.log("🔥 Attempting to delete from S3...");
        console.log("🔍 Delete request data:", {
          token: resume.trackingToken,
          key: resume.key
        });

        const response = await fetch(`http://localhost:3001/delete-resume`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token: resume.trackingToken,
            key: resume.key
          })
        });

        console.log("📡 Delete response status:", response.status);

        if (response.ok) {
          const result = await response.json();
          console.log("✅ Resume deleted from S3 successfully:", result);

          // Remove from local state
          setUserResumes(prevResumes =>
            prevResumes.filter(r => r.id !== resume.id)
          );

          // Update stats
          setUserStats(prev => ({
            ...prev,
            totalResumes: prev.totalResumes - 1
          }));

          setError(null);
          console.log("✅ Resume removed from dashboard");

          // Show success message
          alert(`✅ Resume "${resume.title}" deleted successfully!`);
        } else {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
          console.error("❌ Delete failed with status:", response.status, errorData);
          throw new Error(errorData.message || `HTTP ${response.status}`);
        }
      } catch (deleteError) {
        console.error("❌ Failed to delete from S3:", deleteError);

        // For now, still remove from local state for testing
        setUserResumes(prevResumes =>
          prevResumes.filter(r => r.id !== resume.id)
        );

        setError(`Resume removed from dashboard. S3 deletion status: ${deleteError.message}`);

        // Show error but still remove from UI
        alert(`⚠️ Resume removed from dashboard.\nS3 deletion may have failed: ${deleteError.message}`);
      }

      setIsLoading(false);

    } catch (error) {
      console.error("💥 Error in handleDeleteResume:", error);
      setIsLoading(false);
      setError("Failed to delete resume. Please try again.");
    }
  };

  // Handle resume viewing (same as editing for now)
  const handleViewResume = (resume) => {
    handleEditResume(resume);
  };

  // Format date for display
  const formatDate = (timestamp) => {
    try {
      const date = new Date(parseInt(timestamp) * 1000);
      return date.toLocaleDateString();
    } catch (error) {
      return 'Unknown';
    }
  };

  // Refresh resumes list
  const refreshResumes = async () => {
    if (!auth.user?.profile?.email) return;

    try {
      console.log("🔄 Refreshing resumes...");

      let resumes = [];

      try {
        // Try to fetch real resumes from S3 for this specific user
        const response = await fetch(`http://localhost:3001/list-rewritten-resumes?user_email=${encodeURIComponent(auth.user.profile.email)}`);

        if (response.ok) {
          const allResumes = await response.json();

          // Convert S3 file list to resume format and filter by user
          const recentResumes = allResumes
            .sort((a, b) => new Date(b.LastModified) - new Date(a.LastModified))
            .slice(0, 5); // Show only 5 most recent resumes as "user's resumes"

          resumes = recentResumes.map((file, index) => {
            const fileName = file.Key.split('/').pop().replace('.json', '');
            const parts = fileName.split('-');
            const trackingToken = parts[0];
            const originalName = parts.slice(1).join('-') || 'Resume';

            return {
              id: trackingToken,
              title: originalName.replace(/\.(pdf|doc|docx)$/i, ''),
              name: 'User',
              lastModified: file.LastModified,
              createdAt: new Date(file.LastModified).getTime() / 1000,
              size: file.Size,
              key: file.Key,
              trackingToken: trackingToken,
              originalFilename: originalName
            };
          });

          // Sort by creation time (most recent first)
          resumes.sort((a, b) => b.createdAt - a.createdAt);
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (fetchError) {
        console.warn("⚠️ Refresh failed, keeping current resumes:", fetchError.message);
        return; // Don't update if refresh fails
      }

      setUserResumes(resumes);

      setUserStats(prev => ({
        ...prev,
        totalResumes: resumes.length
      }));

      console.log("✅ Resumes refreshed:", resumes.length);
    } catch (error) {
      console.error("❌ Error refreshing resumes:", error);
    }
  };
  return (
    <div className="dashboard-container">
      {/* New Professional Sidebar */}
      <DashboardSidebar />



      {/* Main Content */}
      <div className="dashboard-main">
        {/* Header */}
        <div className="dashboard-header">
          <h1>Dashboard</h1>
        </div>

        {/* Stats Cards */}
        <div className="stats-grid">
          <div className="stat-card blue">
            <div className="stat-header">
              <div className="stat-label">TOTAL RESUMES</div>
              <div className="stat-indicator blue"></div>
            </div>
            <div className="stat-number">{userStats.totalResumes}</div>
            <div className="stat-change positive">+{Math.max(0, userStats.totalResumes - 1)} from last month</div>
          </div>

          <div className="stat-card green">
            <div className="stat-header">
              <div className="stat-label">COVER LETTERS</div>
              <div className="stat-indicator green"></div>
            </div>
            <div className="stat-number">{userStats.totalCoverLetters}</div>
            <div className="stat-change positive">Coming soon</div>
          </div>
          <div className="stat-card orange">
            <div className="stat-header">
              <div className="stat-label">TEMPLATES</div>
              <div className="stat-indicator orange"></div>
            </div>
            <div className="stat-number">{userStats.totalTemplates}</div>
            <div className="stat-change positive">Available now</div>
          </div>
        </div>

        {/* My Resumes Section */}
        <div className="section">
          <div className="section-header">
            <h2>My Resumes</h2>
            <button
              className="create-btn"
              onClick={() => setIsUploadModalOpen(true)}
            >
              Create New
            </button>
          </div>
          <div className="resumes-grid">
            {isLoading ? (
              <div className="loading-message">
                <p>Loading your resumes...</p>
              </div>
            ) : error ? (
              <div className="error-message">
                <p>{error}</p>
                <button onClick={refreshResumes} className="retry-btn">Try Again</button>
              </div>
            ) : userResumes.length === 0 ? (
              <div className="empty-state">
                <p>No resumes yet. Create your first resume to get started!</p>
                <button
                  className="create-btn"
                  onClick={() => setIsUploadModalOpen(true)}
                >
                  Create Your First Resume
                </button>
              </div>
            ) : (
              userResumes.map((resume) => (
                <div key={resume.id} className="resume-card">
                  <div className="resume-card-content">
                    <ResumePreview
                      resume={resume}
                      onClick={() => {
                        console.log("🖱️ Resume card clicked:", resume);
                        handleEditResume(resume);
                      }}
                    />

                    {/* Hover Actions */}
                    <div className="resume-hover-actions">
                      <button
                        className="hover-action-btn edit-action"
                        onClick={(e) => {
                          e.stopPropagation();
                          console.log("🖱️ Edit button clicked:", resume);
                          handleEditResume(resume);
                        }}
                        title="Edit Resume"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                          <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                      </button>
                      <button
                        className="hover-action-btn delete-action"
                        onClick={(e) => {
                          e.stopPropagation();
                          console.log("🗑️ Delete button clicked:", resume);
                          handleDeleteResume(resume.id);
                        }}
                        title="Delete Resume"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <polyline points="3,6 5,6 21,6"/>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                          <line x1="10" y1="11" x2="10" y2="17"/>
                          <line x1="14" y1="11" x2="14" y2="17"/>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <div className="resume-info">
                    <h3 className="resume-title">{resume.originalFilename || resume.name || 'Untitled Resume'}</h3>
                    <div className="resume-meta">
                      <span className="resume-date">Updated {formatDate(resume.createdAt)}</span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* My Cover Letters Section */}
        <div className="section">
          <div className="section-header">
            <h2>My Cover Letters</h2>
            <button className="create-btn">Create New</button>
          </div>
          <div className="resumes-grid">
            <div className="resume-card">
              <div className="resume-preview cover-letter">
                <div className="resume-lines">
                  <div className="line blue"></div>
                  <div className="line"></div>
                  <div className="line"></div>
                  <div className="line"></div>
                  <div className="line short"></div>
                </div>
              </div>
              <div className="resume-info">
                <h3>Software Engineer Cover Letter</h3>
                <p>Updated 1 day ago</p>
                <div className="resume-actions">
                  <button className="action-btn edit">Edit</button>
                  <button className="action-btn view">View</button>
                </div>
              </div>
            </div>

            <div className="resume-card">
              <div className="resume-preview cover-letter">
                <div className="resume-lines">
                  <div className="line blue"></div>
                  <div className="line"></div>
                  <div className="line"></div>
                  <div className="line"></div>
                  <div className="line short"></div>
                </div>
              </div>
              <div className="resume-info">
                <h3>Product Manager Cover Letter</h3>
                <p>Updated 1 week ago</p>
                <div className="resume-actions">
                  <button className="action-btn edit">Edit</button>
                  <button className="action-btn view">View</button>
                </div>
              </div>
            </div>

            <div className="resume-card">
              <div className="resume-preview cover-letter">
                <div className="resume-lines">
                  <div className="line blue"></div>
                  <div className="line"></div>
                  <div className="line"></div>
                  <div className="line"></div>
                  <div className="line short"></div>
                </div>
              </div>
              <div className="resume-info">
                <h3>UX Designer Cover Letter</h3>
                <p>Updated 2 weeks ago</p>
                <div className="resume-actions">
                  <button className="action-btn edit">Edit</button>
                  <button className="action-btn view">View</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity Section */}
        <div className="section">
          <div className="section-header">
            <h2>Recent Activity</h2>
            <button className="view-all-btn">View All</button>
          </div>
          <div className="activity-list">
            <div className="activity-item">
              <div className="activity-icon blue">C</div>
              <div className="activity-content">
                <p>Your "Software Engineer" resume was viewed by a recruiter at Google</p>
                <span className="activity-time">2 min ago</span>
              </div>
            </div>
            <div className="activity-item">
              <div className="activity-icon green">✓</div>
              <div className="activity-content">
                <p>Your "Product Manager" resume was downloaded</p>
                <span className="activity-time">1 day ago</span>
              </div>
            </div>
            <div className="activity-item">
              <div className="activity-icon red">!</div>
              <div className="activity-content">
                <p>You received a rejection email for the "UX Designer" position</p>
                <span className="activity-time">2 days ago</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Resume Upload Modal */}
      <ResumeUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
      />
    </div>
  );
};

export default Dashboard;
