import React from 'react';
import '../styles/GotResumeModal.css';

const GotResumeModal = ({ isOpen, onYes, onNo }) => {
  if (!isOpen) return null;

  return (
    <div className="got-resume-modal-overlay">
      <div className="got-resume-modal">
        <div className="step-indicator">
          <div className="step-number active">1</div>
          <div className="step-number">2</div>
          <div className="step-number">3</div>
          <button className="sign-in-btn">Sign In</button>
        </div>

        <div className="modal-content">
          <div className="logo-section">
            <div className="logo-icon">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="white">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
          </div>

          <h2>Got Resume?</h2>

          <div className="button-group">
            <button className="yes-btn" onClick={onYes}>
              Yes
            </button>
            <button className="no-btn" onClick={onNo}>
              No
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GotResumeModal;
