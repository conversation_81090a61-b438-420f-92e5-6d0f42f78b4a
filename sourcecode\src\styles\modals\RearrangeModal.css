.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }

  .rearrange-modal {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 10000;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .modal-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }

  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 50%;
    transition: background-color 0.2s;
  }

  .close-button:hover {
    background-color: #f0f0f0;
  }

  .modal-subtitle {
    color: #666;
    margin-bottom: 24px;
  }

  .sections-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
  }

  .section-item-rearrange {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f9f9f9;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    transition: all 0.2s;
  }

  .section-item-rearrange:hover {
    background-color: #f0f0f0;
    border-color: #d0d0d0;
  }

  .section-drag-handle {
    cursor: grab;
    color: #999;
    margin-right: 12px;
  }

  .section-drag-handle:active {
    cursor: grabbing;
  }

  .section-name {
    flex: 1;
    font-weight: 500;
  }

  .section-actions {
    display: flex;
    gap: 8px;
  }

  .section-move-btn {
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
  }

  .section-move-btn:hover:not(:disabled) {
    background-color: #f0f0f0;
    border-color: #d0d0d0;
    color: #333;
  }

  .section-move-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  .cancel-button {
    padding: 10px 16px;
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
  }

  .cancel-button:hover {
    background-color: #f5f5f5;
  }

  .save-button {
    padding: 10px 20px;
    background-color: #4a6cf7;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .save-button:hover {
    background-color: #3a5ce5;
  }

  @media (max-width: 600px) {
    .section-item-rearrange {
      padding: 10px 12px;
    }

    .section-actions {
      gap: 4px;
    }

    .section-move-btn {
      width: 28px;
      height: 28px;
    }
  }
