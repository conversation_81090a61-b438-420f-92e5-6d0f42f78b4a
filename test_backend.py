#!/usr/bin/env python3
"""
Quick test to verify the backend token system is working
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3001"

def test_upload_endpoint():
    """Test that upload returns a tracking token"""
    print("🧪 Testing upload endpoint...")
    
    # Create a simple test file
    test_content = "Test resume content for token verification"
    
    try:
        # Create a temporary file
        with open("test_resume.txt", "w") as f:
            f.write(test_content)
        
        # Upload the file
        with open("test_resume.txt", "rb") as f:
            files = {'file': f}
            response = requests.post(f"{BASE_URL}/upload", files=files)
        
        print(f"Upload response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Upload response: {json.dumps(data, indent=2)}")
            
            # Check for tracking token
            tracking_token = data.get('trackingToken')
            if tracking_token:
                print(f"✅ Tracking token received: {tracking_token}")
                return tracking_token
            else:
                print("❌ No tracking token in response!")
                return None
        else:
            print(f"❌ Upload failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None
    finally:
        # Cleanup
        try:
            import os
            os.remove("test_resume.txt")
        except:
            pass

def test_token_retrieval(token):
    """Test retrieving resume by token"""
    print(f"\n🧪 Testing token retrieval for: {token}")
    
    try:
        response = requests.get(f"{BASE_URL}/resume-by-token", params={'token': token})
        
        print(f"Token retrieval response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Token-based retrieval successful!")
            print(f"Resume data keys: {list(data.keys())}")
            return True
        elif response.status_code == 404:
            print("⏳ Resume not processed yet (404 - normal for new uploads)")
            return False
        else:
            print(f"❌ Token retrieval failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Token retrieval error: {e}")
        return False

def test_backend_endpoints():
    """Test all backend endpoints"""
    print("🚀 Testing Backend Token System")
    print("=" * 50)
    
    # Test upload
    token = test_upload_endpoint()
    
    if token:
        # Test token retrieval
        test_token_retrieval(token)
        
        print(f"\n📋 Summary:")
        print(f"✅ Upload endpoint working - returns token: {token}")
        print(f"✅ Token retrieval endpoint accessible")
        print(f"⏳ Resume processing may take time (normal)")
        
    else:
        print(f"\n❌ Backend token system not working correctly!")
        print(f"Check server logs and ensure backend is running")

if __name__ == "__main__":
    test_backend_endpoints()
