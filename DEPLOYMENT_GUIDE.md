# 🚀 CRITICAL DEPLOYMENT GUIDE - Token-Based Resume Tracking

## ⚠️ IMPORTANT: Lambda Function Must Be Redeployed

The Lambda function has been updated with token-based tracking. **You MUST redeploy it** for the fix to work.

## 📦 Step 1: Deploy Updated Lambda Function

### Option A: AWS Console (Recommended)
1. Go to AWS Lambda Console
2. Find your resume processing function
3. Click "Upload from" → ".zip file"
4. Upload the file: `lambda_function/function.zip`
5. Click "Save"

### Option B: AWS CLI
```bash
# Navigate to the lambda_function directory
cd lambda_function

# Update the Lambda function
aws lambda update-function-code \
    --function-name YOUR_FUNCTION_NAME \
    --zip-file fileb://function.zip
```

### Option C: Manual Upload
1. Download the `lambda_function/function.zip` file
2. Go to AWS Lambda Console
3. Select your function
4. Upload the zip file
5. Save changes

## 🔧 Step 2: Verify Lambda Deployment

After deployment, check the Lambda function logs:
1. Go to CloudWatch Logs
2. Find your Lambda function log group
3. Look for these new log messages:
   - `"Extracted tracking token: {token} from key: {key}"`
   - `"Original key: textract-output/{token}-{filename}.pdf"`
   - `"New key: rewritten-resumes/{token}-{filename}.json"`

## 🖥️ Step 3: Restart Backend Servers

### Python Flask Server:
```bash
cd sourcecode/server
python app.py
```

### Node.js Server (if using):
```bash
cd sourcecode/server
npm start
```

## 🌐 Step 4: Test the Frontend

1. Open your application
2. Upload a resume
3. Check browser console for:
   - `"Tracking token received: {uuid}"`
   - `"✅ Tracking token confirmed: {uuid}"`
   - `"Fetching resume with tracking token: {uuid}"`

## 🧪 Step 5: Run Comprehensive Test

Use the provided test script:
```bash
python test_token_system.py
```

This will:
- Simulate multiple concurrent uploads
- Verify each user gets their own resume back
- Detect any cross-contamination issues

## 🔍 Step 6: Monitor and Verify

### Check S3 Bucket Structure:
- **Input files**: `textract-output/{token}-{filename}.pdf`
- **Output files**: `rewritten-resumes/{token}-{filename}.json`

### Verify Token Matching:
- Input: `textract-output/abc123-resume.pdf`
- Output: `rewritten-resumes/abc123-resume.json`
- Both should have the same token: `abc123`

### Check API Endpoints:
- `POST /upload` → Returns `trackingToken`
- `GET /resume-by-token?token={uuid}` → Returns specific resume

## 🚨 Troubleshooting

### Issue: Still getting wrong resumes
**Solution**: Lambda function not deployed
- Redeploy `lambda_function/function.zip`
- Check CloudWatch logs for new debug messages

### Issue: No tracking token in response
**Solution**: Backend not updated
- Restart Python/Node.js server
- Check server logs for token generation

### Issue: Token-based retrieval fails
**Solution**: Check file naming
- Verify S3 files have token in filename
- Check Lambda logs for token extraction

### Issue: 404 errors during polling
**Solution**: Normal behavior
- Resume still processing
- System will retry automatically
- Check after a few minutes

## 📊 Expected Behavior After Fix

### ✅ Correct Behavior:
- Each upload gets unique token
- Token preserved in filename
- User retrieves own resume by token
- No cross-user contamination
- Clear error messages

### ❌ Previous Problem:
- Users got latest resume (anyone's)
- No user-specific tracking
- Cross-contamination possible
- Timing-dependent failures

## 🔐 Security Verification

Run this test to verify the fix:
1. Open 3 browser tabs
2. Upload different resumes simultaneously
3. Each should get their own resume back
4. Check names/content match uploaded files

## 📝 Key Files Changed

- `lambda_function/app.py` - Token extraction and preservation
- `sourcecode/server/controllers/resume_controller.py` - Token-based upload/retrieval
- `sourcecode/server/controllers/resumeController.js` - Node.js version
- `sourcecode/src/services/awsService.js` - Frontend token service
- `sourcecode/src/components/UploadResume.js` - Token-based polling

## 🎯 Success Criteria

✅ Lambda function deployed with new code
✅ Backend servers restarted
✅ Upload returns tracking token
✅ Files stored with token in filename
✅ Retrieval uses token-based lookup
✅ Multiple users get own resumes
✅ No cross-contamination detected

---

**🚨 CRITICAL: The Lambda function MUST be redeployed for this fix to work!**
