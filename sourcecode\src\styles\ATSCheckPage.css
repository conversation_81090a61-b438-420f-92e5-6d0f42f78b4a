.ats-check-page {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.ats-check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  border-bottom: 1px solid #eaeaea;
  background-color: #f9f9f9;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.ats-check-header h2 {
  margin: 0;
  font-size: 22px;
  color: #333;
}

.back-to-resume-button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  color: #555;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.back-to-resume-button:hover {
  background-color: #f0f0f0;
  color: #333;
  border-color: #ccc;
}

.ats-check-content {
  flex: 1;
  padding: 20px 25px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.ats-check-intro {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.ats-check-intro p {
  font-size: 16px;
  color: #555;
  line-height: 1.5;
}

.job-description-section {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.job-description-section h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #333;
}

.job-description-textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 15px;
  resize: vertical;
  transition: border-color 0.3s;
  font-family: inherit;
}

.job-description-textarea:focus {
  border-color: #4a6cf7;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.job-description-hint {
  margin-top: 10px;
  font-size: 14px;
  color: #777;
  font-style: italic;
}

.ats-results-section {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

/* Responsive styles */
@media (max-width: 768px) {
  .ats-check-header {
    padding: 12px 15px;
  }

  .ats-check-header h2 {
    font-size: 18px;
  }

  .ats-check-content {
    padding: 15px;
  }
}
