.design-modal {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }
  
  .design-settings {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  .settings-section {
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
  }
  
  .settings-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
  
  .settings-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }
  
  .slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .slider-label {
    font-size: 12px;
    color: #666;
    width: 70px;
  }
  
  .slider {
    flex: 1;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: #eee;
    outline: none;
    border-radius: 2px;
  }
  
  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4a6cf7;
    cursor: pointer;
  }
  
  .slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4a6cf7;
    cursor: pointer;
    border: none;
  }
  
  .color-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
  }
  
  .color-option:hover {
    transform: scale(1.1);
  }
  
  .color-option.selected {
    box-shadow: 0 0 0 2px white, 0 0 0 4px #4a6cf7;
  }
  
  .select-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
  }
  
  .select-input:focus {
    border-color: #4a6cf7;
  }
  
  .font-size-controls {
    display: flex;
    gap: 10px;
  }
  
  .size-button {
    flex: 1;
    padding: 8px 0;
    border: 1px solid #eee;
    background: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .size-button:nth-child(1) {
    font-size: 12px;
  }
  
  .size-button:nth-child(2) {
    font-size: 16px;
  }
  
  .size-button:nth-child(3) {
    font-size: 20px;
  }
  
  .size-button.active {
    border-color: #4a6cf7;
    background-color: rgba(74, 108, 247, 0.1);
  }
  
  .background-options {
    display: flex;
    gap: 15px;
  }
  
  .background-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }
  
  .bg-preview {
    width: 80px;
    height: 50px;
    border-radius: 6px;
    border: 1px solid #eee;
  }
  
  .bg-preview.solid {
    background-color: white;
  }
  
  .bg-preview.gradient {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
  
  .bg-preview.pattern {
    background-color: white;
    background-image: radial-gradient(#4a6cf7 1px, transparent 1px);
    background-size: 10px 10px;
  }
  
  .background-option.selected .bg-preview {
    border-color: #4a6cf7;
    box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.3);
  }
  
  .background-option span {
    font-size: 12px;
    color: #666;
  }
  