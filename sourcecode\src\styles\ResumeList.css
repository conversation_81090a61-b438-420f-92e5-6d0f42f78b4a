.resume-list-container {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .resume-list-container h2 {
    margin-bottom: 16px;
    color: #333;
    font-size: 20px;
  }
  
  .resume-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .resume-list-item {
    margin-bottom: 8px;
  }
  
  .resume-list-item button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border: 1px solid #eaeaea;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
  }
  
  .resume-list-item button:hover {
    background-color: #eef2ff;
    border-color: #4a6cf7;
  }
  
  .resume-filename {
    font-weight: 500;
    color: #333;
  }
  
  .resume-date {
    font-size: 12px;
    color: #666;
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #eaeaea;
    border-top: 4px solid #4a6cf7;
    border-radius: 50%;
    margin: 20px auto;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .error-message {
    color: #dc2626;
    margin-bottom: 16px;
  }
  
  .retry-button {
    padding: 8px 16px;
    background-color: #4a6cf7;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .retry-button:hover {
    background-color: #3a5ce5;
  }
  