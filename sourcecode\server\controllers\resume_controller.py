import os
import json
import logging
import uuid
import time
import boto3
from flask import jsonify
from werkzeug.utils import secure_filename

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configure AWS S3
s3 = boto3.client(
    's3',
    aws_access_key_id=os.environ.get('AWS_ACCESS_KEY_ID'),
    aws_secret_access_key=os.environ.get('AWS_SECRET_ACCESS_KEY'),
    region_name=os.environ.get('AWS_REGION', 'us-east-1')
)

def upload_resume(request):
    """
    Upload a resume file to S3 with tracking token and user association
    """
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        # Get user information from request headers or form data
        user_email = request.form.get('userEmail') or request.headers.get('X-User-Email')
        user_id = request.form.get('userId') or request.headers.get('X-User-ID')

        if not user_email and not user_id:
            return jsonify({'error': 'User identification required'}), 400

        # Generate a unique tracking token
        tracking_token = str(uuid.uuid4())
        original_filename = secure_filename(file.filename)
        key = f"textract-output/{tracking_token}-{original_filename}"

        # Upload to S3 with metadata including the tracking token and user info
        upload_timestamp = str(int(time.time()))
        metadata = {
            'tracking-token': tracking_token,
            'original-filename': original_filename,
            'upload-timestamp': upload_timestamp,
            'user-email': user_email or '',
            'user-id': user_id or ''
        }

        s3.upload_fileobj(
            file,
            os.environ.get('AWS_S3_BUCKET'),
            key,
            ExtraArgs={
                'ContentType': file.content_type,
                'Metadata': metadata
            }
        )

        logger.info(f'File uploaded successfully with tracking token: {tracking_token}')

        return jsonify({
            'message': 'File uploaded successfully',
            'trackingToken': tracking_token,
            'data': {
                'Bucket': os.environ.get('AWS_S3_BUCKET'),
                'Key': key,
                'TrackingToken': tracking_token
            }
        })
    except Exception as error:
        logger.error(f'Upload error: {error}')
        return jsonify({'error': 'Error uploading file'}), 500


def get_resume_by_token(request):
    """
    Get a processed resume by its tracking token
    """
    try:
        tracking_token = request.args.get('token')
        if not tracking_token:
            return jsonify({'error': 'Tracking token is required'}), 400

        logger.info(f'Fetching resume with tracking token: {tracking_token}')

        # List all files in the rewritten-resumes folder
        list_params = {
            'Bucket': os.environ.get('AWS_S3_BUCKET'),
            'Prefix': 'rewritten-resumes/'
        }

        data = s3.list_objects_v2(**list_params)
        files = data.get('Contents', [])

        if not files:
            return jsonify({
                'error': 'No processed resumes found',
                'message': 'The resume may still be processing. Please try again in a few moments.'
            }), 404

        # Find the file with the matching tracking token
        target_file = None
        for file_obj in files:
            file_key = file_obj['Key']
            # Check if the tracking token is in the filename (more precise matching)
            # Expected format: rewritten-resumes/{token}-{filename}.json
            filename_part = file_key.split('/')[-1]  # Get just the filename
            if filename_part.startswith(tracking_token + '-'):
                target_file = file_obj
                logger.info(f'Found matching file: {file_key} for token: {tracking_token}')
                break

        # If not found with exact match, try broader search
        if not target_file:
            for file_obj in files:
                file_key = file_obj['Key']
                if tracking_token in file_key:
                    target_file = file_obj
                    logger.info(f'Found file with token in name: {file_key} for token: {tracking_token}')
                    break

        if not target_file:
            return jsonify({
                'error': 'Resume not found',
                'message': f'No processed resume found for tracking token: {tracking_token}. The resume may still be processing.'
            }), 404

        # Get the file content
        get_params = {
            'Bucket': os.environ.get('AWS_S3_BUCKET'),
            'Key': target_file['Key']
        }

        try:
            file_data = s3.get_object(**get_params)
            file_content = file_data["Body"].read()

            logger.info(f'Retrieved resume for token {tracking_token}, size: {len(file_content)}')

            json_content = json.loads(file_content.decode('utf-8'))
            logger.info('JSON parsed successfully')

            return jsonify(json_content)

        except Exception as get_error:
            logger.error(f'Error getting object: {get_error}')
            return jsonify({
                'error': 'Error retrieving resume',
                'message': str(get_error)
            }), 500

    except Exception as error:
        logger.error(f'Error fetching resume by token: {error}')
        return jsonify({
            'error': 'Error fetching resume',
            'message': str(error)
        }), 500

def get_rewritten_resume(request):
    """
    Get a rewritten resume from S3 by key
    """
    try:
        key = request.args.get('key')
        logger.info(f'Fetching rewritten resume with key: {key}')

        if not key:
            return jsonify({'error': 'Missing key parameter'}), 400

        # First try to find the exact file with the key
        try:
            params = {
                'Bucket': os.environ.get('AWS_S3_BUCKET'),
                'Key': f"rewritten-resumes/{key}"  # Try the exact key first
            }

            logger.info(f'Trying exact key S3 params: {params}')
            data = s3.get_object(**params)
            # Read the content once and store it
            file_content = data["Body"].read()
            logger.info(f'S3 data received with exact key, content length: {len(file_content)}')

            json_content = json.loads(file_content.decode('utf-8'))
            logger.info('JSON parsed successfully')

            return jsonify(json_content)
        except Exception as exact_key_error:
            logger.info('Exact key not found, trying to find file with key in the name...')

            # If exact key fails, try to find a file that contains the key in its name
            try:
                # List all files in the rewritten-resumes folder
                list_params = {
                    'Bucket': os.environ.get('AWS_S3_BUCKET'),
                    'Prefix': 'rewritten-resumes/'
                }

                list_data = s3.list_objects_v2(**list_params)
                logger.info(f'Found {len(list_data.get("Contents", []))} objects in the bucket')

                # Filter out the folder itself and only return actual files
                files = [item for item in list_data.get('Contents', []) if not item['Key'].endswith('/')]
                logger.info(f'Found {len(files)} files after filtering')

                # Try to find a file that contains the key in its name
                matching_file = next((file for file in files if key in file['Key']), None)

                if matching_file:
                    logger.info(f'Found matching file: {matching_file["Key"]}')

                    get_params = {
                        'Bucket': os.environ.get('AWS_S3_BUCKET'),
                        'Key': matching_file['Key']
                    }

                    file_data = s3.get_object(**get_params)
                    # Read the content once and store it
                    file_content = file_data["Body"].read()
                    logger.info(f'File content received, size: {len(file_content)}')

                    json_content = json.loads(file_content.decode('utf-8'))
                    logger.info('JSON parsed successfully')

                    return jsonify(json_content)
                else:
                    # If no matching file is found, return the latest file
                    logger.info('No matching file found, returning the latest file...')

                    # Sort by LastModified date (newest first)
                    files.sort(key=lambda x: x['LastModified'], reverse=True)

                    if files:
                        # Get the most recent file
                        latest_file = files[0]
                        logger.info(f'Latest file: {latest_file["Key"]}, Last modified: {latest_file["LastModified"]}')

                        get_params = {
                            'Bucket': os.environ.get('AWS_S3_BUCKET'),
                            'Key': latest_file['Key']
                        }

                        file_data = s3.get_object(**get_params)
                        # Read the content once and store it
                        file_content = file_data["Body"].read()
                        logger.info(f'Latest file content received, size: {len(file_content)}')

                        json_content = json.loads(file_content.decode('utf-8'))
                        logger.info('JSON parsed successfully')

                        return jsonify(json_content)
                    else:
                        return jsonify({
                            'error': 'No resumes found in the bucket',
                            'details': f'No files found in rewritten-resumes/ in bucket {os.environ.get("AWS_S3_BUCKET")}'
                        }), 404
            except Exception as list_error:
                logger.error(f'Error listing or processing files: {list_error}')
                raise list_error
    except Exception as error:
        logger.error(f'Fetch rewritten resume error: {error}')
        return jsonify({
            'error': 'Error fetching resume',
            'message': str(error)
        }), 500


def get_user_resumes(request):
    """
    Get all processed resumes for a specific user, sorted by creation time (most recent first)
    """
    try:
        user_email = request.args.get('userEmail') or request.headers.get('X-User-Email')
        user_id = request.args.get('userId') or request.headers.get('X-User-ID')

        if not user_email and not user_id:
            return jsonify({'error': 'User identification required'}), 400

        logger.info(f'Fetching resumes for user: {user_email or user_id}')

        # List all files in the rewritten-resumes folder
        list_params = {
            'Bucket': os.environ.get('AWS_S3_BUCKET'),
            'Prefix': 'rewritten-resumes/'
        }

        data = s3.list_objects_v2(**list_params)
        files = data.get('Contents', [])

        if not files:
            return jsonify({
                'resumes': [],
                'message': 'No resumes found'
            })

        user_resumes = []

        # Check each file's metadata to find user's resumes
        for file_obj in files:
            file_key = file_obj['Key']

            try:
                # Get object metadata
                head_response = s3.head_object(
                    Bucket=os.environ.get('AWS_S3_BUCKET'),
                    Key=file_key
                )

                metadata = head_response.get('Metadata', {})
                file_user_email = metadata.get('user-email', '')
                file_user_id = metadata.get('user-id', '')

                # Check if this resume belongs to the requesting user
                user_match = False
                if user_email and file_user_email == user_email:
                    user_match = True
                elif user_id and file_user_id == user_id:
                    user_match = True

                if user_match:
                    # Get the resume content
                    get_response = s3.get_object(
                        Bucket=os.environ.get('AWS_S3_BUCKET'),
                        Key=file_key
                    )

                    resume_content = json.loads(get_response['Body'].read().decode('utf-8'))

                    # Extract resume info
                    resume_info = {
                        'id': metadata.get('tracking-token', file_key.split('/')[-1].split('.')[0]),
                        'title': resume_content.get('basics', {}).get('title', 'Untitled Resume'),
                        'name': resume_content.get('basics', {}).get('name', 'Unknown'),
                        'lastModified': file_obj['LastModified'].isoformat(),
                        'createdAt': metadata.get('upload-timestamp', str(int(file_obj['LastModified'].timestamp()))),
                        'size': file_obj['Size'],
                        'key': file_key,
                        'trackingToken': metadata.get('tracking-token', ''),
                        'originalFilename': metadata.get('original-filename', ''),
                        'views': 0  # TODO: Implement view tracking
                    }

                    user_resumes.append(resume_info)

            except Exception as file_error:
                logger.warning(f'Error processing file {file_key}: {file_error}')
                continue

        # Sort by creation time (most recent first)
        user_resumes.sort(key=lambda x: float(x['createdAt']), reverse=True)

        logger.info(f'Found {len(user_resumes)} resumes for user {user_email or user_id}')

        return jsonify({
            'resumes': user_resumes,
            'total': len(user_resumes)
        })

    except Exception as error:
        logger.error(f'Error fetching user resumes: {error}')
        return jsonify({
            'error': 'Error fetching user resumes',
            'message': str(error)
        }), 500


def update_resume(request):
    """
    Update an existing resume with new data
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        resume_id = data.get('resumeId') or data.get('id')
        resume_data = data.get('resumeData')
        user_email = data.get('userEmail') or request.headers.get('X-User-Email')
        user_id = data.get('userId') or request.headers.get('X-User-ID')

        if not resume_id:
            return jsonify({'error': 'Resume ID is required'}), 400

        if not resume_data:
            return jsonify({'error': 'Resume data is required'}), 400

        if not user_email and not user_id:
            return jsonify({'error': 'User identification required'}), 400

        logger.info(f'Updating resume {resume_id} for user: {user_email or user_id}')

        # Find the resume file by tracking token
        list_params = {
            'Bucket': os.environ.get('AWS_S3_BUCKET'),
            'Prefix': 'rewritten-resumes/'
        }

        list_data = s3.list_objects_v2(**list_params)
        files = list_data.get('Contents', [])

        target_file = None
        for file_obj in files:
            file_key = file_obj['Key']
            if resume_id in file_key:
                # Verify ownership
                try:
                    head_response = s3.head_object(
                        Bucket=os.environ.get('AWS_S3_BUCKET'),
                        Key=file_key
                    )

                    metadata = head_response.get('Metadata', {})
                    file_user_email = metadata.get('user-email', '')
                    file_user_id = metadata.get('user-id', '')

                    # Check if this resume belongs to the requesting user
                    user_match = False
                    if user_email and file_user_email == user_email:
                        user_match = True
                    elif user_id and file_user_id == user_id:
                        user_match = True

                    if user_match:
                        target_file = file_key
                        break

                except Exception as check_error:
                    logger.warning(f'Error checking file ownership {file_key}: {check_error}')
                    continue

        if not target_file:
            return jsonify({
                'error': 'Resume not found or access denied',
                'message': f'Resume {resume_id} not found for this user'
            }), 404

        # Update the resume content
        updated_content = json.dumps(resume_data, indent=2)

        # Get existing metadata and update timestamp
        head_response = s3.head_object(
            Bucket=os.environ.get('AWS_S3_BUCKET'),
            Key=target_file
        )

        existing_metadata = head_response.get('Metadata', {})
        existing_metadata['last-modified'] = str(int(time.time()))

        # Upload the updated content
        s3.put_object(
            Bucket=os.environ.get('AWS_S3_BUCKET'),
            Key=target_file,
            Body=updated_content.encode('utf-8'),
            ContentType='application/json',
            Metadata=existing_metadata
        )

        logger.info(f'Successfully updated resume {resume_id}')

        return jsonify({
            'message': 'Resume updated successfully',
            'resumeId': resume_id,
            'lastModified': existing_metadata['last-modified']
        })

    except Exception as error:
        logger.error(f'Error updating resume: {error}')
        return jsonify({
            'error': 'Error updating resume',
            'message': str(error)
        }), 500
