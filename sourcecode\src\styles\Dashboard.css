.dashboard-container {
  display: flex;
  height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Sidebar */
.dashboard-sidebar {
  width: 260px;
  background-color: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid #f1f5f9;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 28px;
  height: 28px;
  background: #3b82f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.sidebar-nav {
  flex: 1;
  padding: 8px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  font-weight: 500;
  margin: 2px 12px;
  border-radius: 8px;
}

.nav-item:hover {
  background-color: #f1f5f9;
  color: #3b82f6;
}

.nav-item.active {
  background-color: #3b82f6;
  color: white;
}

.nav-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #f1f5f9;
}

.user-profile-sidebar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-sidebar {
  width: 36px;
  height: 36px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.user-info-sidebar {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name-sidebar {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.user-email-sidebar {
  font-size: 12px;
  color: #64748b;
}

/* Main Content */
.dashboard-main {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
  background-color: #f8fafc;
}

.dashboard-header {
  margin-bottom: 32px;
}

.dashboard-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  position: relative;
}

.stat-card.blue {
  border-top: 4px solid #3b82f6;
}

.stat-card.green {
  border-top: 4px solid #10b981;
}

.stat-card.orange {
  border-top: 4px solid #f59e0b;
}

.stat-card.red {
  border-top: 4px solid #ef4444;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-label {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.stat-indicator.blue {
  background: #3b82f6;
}

.stat-indicator.green {
  background: #10b981;
}

.stat-indicator.orange {
  background: #f59e0b;
}

.stat-indicator.red {
  background: #ef4444;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

/* Sections */
.section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.create-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background: #2563eb;
}

.view-all-btn {
  background: transparent;
  color: #3b82f6;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.create-btn:hover {
  background: #2563eb;
}

.view-all-btn {
  background: transparent;
  color: #3b82f6;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

/* Resume Grid */
.resumes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.resume-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
}

.resume-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.resume-preview {
  height: 120px;
  background: #f8fafc;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resume-lines {
  width: 100%;
  max-width: 120px;
}

.line {
  height: 3px;
  background: #e2e8f0;
  margin-bottom: 8px;
  border-radius: 2px;
}

.line.blue {
  background: #3b82f6;
  height: 4px;
}

.line.short {
  width: 60%;
}

.resume-info {
  padding: 20px;
}

.resume-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.resume-info p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 16px 0;
}

.resume-views {
  font-size: 12px;
  color: #3b82f6;
  margin: 0 0 16px 0;
}

.resume-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid;
}

.action-btn.edit {
  background: transparent;
  color: #64748b;
  border-color: #e2e8f0;
}

.action-btn.edit:hover {
  background: #f1f5f9;
  color: #3b82f6;
  border-color: #3b82f6;
}

.action-btn.view {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.action-btn.view:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.action-btn.delete {
  background: transparent;
  color: #dc2626;
  border-color: #fecaca;
}

.action-btn.delete:hover {
  background: #fef2f2;
  color: #b91c1c;
  border-color: #dc2626;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn:disabled:hover {
  background: inherit;
  color: inherit;
  border-color: inherit;
}

/* Resume Content Preview */
.resume-content-preview {
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Times New Roman', serif;
  font-size: 10px;
  line-height: 1.2;
  color: #333;
  background: white;
}

.preview-header {
  text-align: center;
  margin-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 6px;
}

.preview-name {
  font-size: 12px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.preview-subtitle {
  font-size: 8px;
  color: #6b7280;
  font-style: italic;
}

.preview-sections {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.preview-section {
  flex: 1;
}

.section-title {
  font-size: 9px;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 3px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  border-bottom: 1px solid #3b82f6;
  padding-bottom: 1px;
}

.section-line {
  height: 2px;
  background: #e5e7eb;
  margin-bottom: 2px;
  border-radius: 1px;
}

.section-line.short {
  width: 70%;
}

/* Resume Card Content */
.resume-card-content {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

/* Resume Preview Thumbnail */
.resume-preview-thumbnail {
  width: 100%;
  height: 220px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.resume-preview-thumbnail:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.resume-page {
  width: 100%;
  height: 100%;
  padding: 12px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 7px;
  line-height: 1.2;
  color: #1f2937;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.resume-header {
  text-align: center;
  margin-bottom: 6px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 1px;
}

.resume-name {
  font-size: 11px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1.1;
  word-break: break-word;
  hyphens: auto;
}

.resume-title {
  font-size: 8px;
  color: #3b82f6;
  margin-bottom: 3px;
  font-weight: 500;
  line-height: 1.2;
}

.resume-contact {
  display: flex;
  justify-content: center;
  gap: 6px;
  flex-wrap: wrap;
  margin-top: 2px;
}

.contact-item {
  font-size: 6px;
  color: #6b7280;
  line-height: 1.1;
}

.resume-section {
  margin-bottom: 6px;
  flex-shrink: 0;
}

.section-title {
  font-size: 7px;
  font-weight: 700;
  color: #1f2937;
  text-transform: uppercase;
  letter-spacing: 0.4px;
  margin-bottom: 3px;
  padding-bottom: 1px;
  border-bottom: 1px solid #3b82f6;
  line-height: 1;
}

.section-content {
  padding-left: 3px;
}

.text-line {
  font-size: 6px;
  color: #4b5563;
  line-height: 1.3;
  margin-bottom: 1px;
}

.experience-item, .education-item {
  margin-bottom: 3px;
  line-height: 1.2;
}

.exp-title, .edu-degree {
  font-size: 7px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1px;
  line-height: 1.1;
}

.exp-company, .edu-school {
  font-size: 6px;
  color: #3b82f6;
  font-weight: 500;
  margin-bottom: 1px;
  line-height: 1.1;
}

.exp-dates {
  font-size: 5px;
  color: #6b7280;
  line-height: 1;
}

.skills-list {
  font-size: 6px;
  color: #4b5563;
  line-height: 1.4;
  word-break: break-word;
}

/* Loading state */
.resume-preview-loading {
  width: 100%;
  height: 200px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.loading-placeholder {
  width: 80%;
  height: 80%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
}

.loading-line {
  height: 3px;
  background: #e5e7eb;
  border-radius: 2px;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-line.short {
  width: 60%;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Hover Actions */
.resume-hover-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 6px;
  opacity: 0;
  transform: translateY(-4px);
  transition: all 0.2s ease;
  z-index: 10;
}

.resume-card:hover .resume-hover-actions {
  opacity: 1;
  transform: translateY(0);
}

.hover-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.edit-action {
  background: rgba(59, 130, 246, 0.9);
  color: white;
}

.edit-action:hover {
  background: rgba(59, 130, 246, 1);
  transform: scale(1.05);
}

.delete-action {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.delete-action:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.05);
}

/* Resume Info Styling */
.resume-info {
  padding: 16px;
  background: white;
}

.resume-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
  line-height: 1.4;
  text-align: center;
  word-break: break-word;
  hyphens: auto;
}

.resume-meta {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
  margin-top: 8px;
}

.resume-date {
  margin: 0;
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 24px 0;
}

/* Activity List */
.activity-list {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.activity-icon.blue {
  background: #dbeafe;
  color: #3b82f6;
}

.activity-icon.green {
  background: #dcfce7;
  color: #16a34a;
}

.activity-icon.red {
  background: #fee2e2;
  color: #dc2626;
}

.activity-content {
  flex: 1;
}

.activity-content p {
  font-size: 14px;
  color: #1e293b;
  margin: 0 0 4px 0;
  line-height: 1.5;
}

.activity-time {
  font-size: 12px;
  color: #64748b;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }
  
  .dashboard-sidebar {
    width: 100%;
    height: auto;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .resumes-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading, Error, and Empty States */
.loading-message, .error-message, .empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.loading-message p, .error-message p, .empty-state p {
  color: #64748b;
  font-size: 16px;
  margin-bottom: 16px;
}

.retry-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #4338ca;
}

.empty-state .create-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.empty-state .create-btn:hover {
  background: #4338ca;
}

/* Resume card enhancements */
.resume-name {
  color: #64748b;
  font-size: 14px;
  margin: 4px 0;
}

.resume-date {
  color: #94a3b8;
  font-size: 12px;
  margin: 4px 0;
}
